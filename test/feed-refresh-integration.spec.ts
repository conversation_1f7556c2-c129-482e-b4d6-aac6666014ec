import { Test, TestingModule } from '@nestjs/testing';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { ContentPublishedListener } from '../src/feed/listener/content-published.listener';
import { FeedService } from '../src/feed/service/feed.service';
import { FeedCacheService } from '../src/feed/service/feed-cache.service';

describe('Feed Refresh Integration', () => {
  let contentPublishedListener: ContentPublishedListener;
  let feedService: FeedService;
  let feedCacheService: FeedCacheService;
  let eventEmitter: EventEmitter2;

  // Mock services
  const mockFeedService = {
    addContentToUserFeed: jest.fn(),
  };

  const mockFeedCacheService = {
    invalidateDiscoveryFeed: jest.fn(),
    invalidateUserFeed: jest.fn(),
  };

  const mockLogger = {
    debug: jest.fn(),
    log: jest.fn(),
    error: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContentPublishedListener,
        EventEmitter2,
        {
          provide: FeedService,
          useValue: mockFeedService,
        },
        {
          provide: FeedCacheService,
          useValue: mockFeedCacheService,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    contentPublishedListener = module.get<ContentPublishedListener>(
      ContentPublishedListener,
    );
    feedService = module.get<FeedService>(FeedService);
    feedCacheService = module.get<FeedCacheService>(FeedCacheService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Image Published Event', () => {
    it('should handle image published event and update feeds', async () => {
      const imagePublishedEvent = {
        id: 'test-image-id',
        userId: 'test-user-id',
        publishedAt: new Date(),
      };

      // Ensure mocks resolve successfully
      mockFeedService.addContentToUserFeed.mockResolvedValue(undefined);
      mockFeedCacheService.invalidateDiscoveryFeed.mockResolvedValue(undefined);

      await contentPublishedListener.handleImagePublishedEvent(
        imagePublishedEvent,
      );

      expect(mockFeedService.addContentToUserFeed).toHaveBeenCalledWith(
        'test-user-id',
        'image',
        'test-image-id',
      );
      expect(mockFeedCacheService.invalidateDiscoveryFeed).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Image published event processed successfully for feeds',
        {
          imageId: 'test-image-id',
          userId: 'test-user-id',
        },
      );
    });

    it('should handle errors gracefully during image published event', async () => {
      const imagePublishedEvent = {
        id: 'test-image-id',
        userId: 'test-user-id',
        publishedAt: new Date(),
      };

      mockFeedService.addContentToUserFeed.mockRejectedValue(
        new Error('Feed service error'),
      );

      await contentPublishedListener.handleImagePublishedEvent(
        imagePublishedEvent,
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to handle image published event for feeds',
        expect.objectContaining({
          event: imagePublishedEvent,
          error: 'Feed service error',
        }),
      );
    });
  });

  describe('Video Published Event', () => {
    it('should handle video published event and update feeds', async () => {
      const videoPublishedEvent = {
        id: 'test-video-id',
        userId: 'test-user-id',
        publishedAt: new Date(),
      };

      // Ensure mocks resolve successfully
      mockFeedService.addContentToUserFeed.mockResolvedValue(undefined);
      mockFeedCacheService.invalidateDiscoveryFeed.mockResolvedValue(undefined);

      await contentPublishedListener.handleVideoPublishedEvent(
        videoPublishedEvent,
      );

      expect(mockFeedService.addContentToUserFeed).toHaveBeenCalledWith(
        'test-user-id',
        'video',
        'test-video-id',
      );
      expect(mockFeedCacheService.invalidateDiscoveryFeed).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Video published event processed successfully for feeds',
        {
          videoId: 'test-video-id',
          userId: 'test-user-id',
        },
      );
    });

    it('should handle errors gracefully during video published event', async () => {
      const videoPublishedEvent = {
        id: 'test-video-id',
        userId: 'test-user-id',
        publishedAt: new Date(),
      };

      mockFeedService.addContentToUserFeed.mockRejectedValue(
        new Error('Feed service error'),
      );

      await contentPublishedListener.handleVideoPublishedEvent(
        videoPublishedEvent,
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to handle video published event for feeds',
        expect.objectContaining({
          event: videoPublishedEvent,
          error: 'Feed service error',
        }),
      );
    });
  });

  describe('Event Integration', () => {
    it('should register event listeners correctly', () => {
      // Verify that the listener is properly decorated with @OnEvent
      const imageHandler = contentPublishedListener.handleImagePublishedEvent;
      const videoHandler = contentPublishedListener.handleVideoPublishedEvent;

      expect(imageHandler).toBeDefined();
      expect(videoHandler).toBeDefined();
      expect(typeof imageHandler).toBe('function');
      expect(typeof videoHandler).toBe('function');
    });
  });
});
