import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('User Likes Endpoint (E2E)', () => {
  let app: INestApplication;
  let userToken: string;
  let userId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Authenticate to get token
    const authResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        username: 'mischstrotz',
        password: 'L3tzAI@2023!',
      })
      .expect(200);

    userToken = authResponse.body.jwtToken;
    userId = '5ed74083-f9d1-4897-b8e3-c8f1596af767'; // mischstrotz user ID
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Limit Parameter', () => {
    it('should respect limit=1', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ limit: 1 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeLessThanOrEqual(1);
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.limit).toBe(1);
    });

    it('should respect limit=5', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ limit: 5 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data.length).toBeLessThanOrEqual(5);
      expect(response.body.pagination.limit).toBe(5);
    });

    it('should respect limit=16', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ limit: 16 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data.length).toBeLessThanOrEqual(16);
      expect(response.body.pagination.limit).toBe(16);
    });
  });

  describe('Cursor Pagination', () => {
    it('should support cursor-based pagination', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ cursor: '', limit: 5 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.hasMore).toBeDefined();
      expect(response.body.pagination.count).toBeDefined();
    });

    it('should handle empty cursor parameter', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ cursor: '', limit: 10 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.items).toBeDefined();
      expect(response.body.pagination.hasMore).toBeDefined();
    });
  });

  describe('ModelIds Filtering', () => {
    it('should accept modelIds parameter without error', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({
          'modelIds[]': '550e8400-e29b-41d4-a716-************',
          limit: 10,
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should accept multiple modelIds', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({
          'modelIds[]': [
            '550e8400-e29b-41d4-a716-************',
            '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
          ],
          limit: 10,
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data).toBeDefined();
    });

    it('should handle invalid modelIds gracefully', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({
          'modelIds[]': ['invalid-uuid', 'another-invalid'],
          limit: 10,
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data).toBeDefined();
    });

    it('should filter results when valid modelIds are provided', async () => {
      // Get baseline without filter
      const baselineResponse = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ limit: 20 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      // Get filtered results
      const filteredResponse = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({
          'modelIds[]': '550e8400-e29b-41d4-a716-************',
          limit: 20,
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      // The filtering should either reduce the number of results or return the same
      // (if all items happen to match the filter)
      expect(filteredResponse.body.data.length).toBeLessThanOrEqual(
        baselineResponse.body.data.length,
      );
    });
  });

  describe('Response Structure', () => {
    it('should return proper response structure for offset pagination', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ page: 1, limit: 5 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.pagination).toHaveProperty('total');
      expect(response.body.pagination).toHaveProperty('page');
      expect(response.body.pagination).toHaveProperty('limit');
      expect(response.body.pagination).toHaveProperty('totalPages');
    });

    it('should return proper response structure for cursor pagination', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ cursor: '', limit: 5 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.pagination).toHaveProperty('count');
      expect(response.body.pagination).toHaveProperty('hasMore');
    });

    it('should include entity information in likes', async () => {
      const response = await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ limit: 1 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      if (response.body.data.length > 0) {
        const like = response.body.data[0];
        expect(like).toHaveProperty('id');
        expect(like).toHaveProperty('entityType');
        expect(like).toHaveProperty('entityId');
        expect(like).toHaveProperty('createdAt');
        expect(like).toHaveProperty('user');
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid user ID', async () => {
      const response = await request(app.getHttpServer())
        .get('/social/users/invalid-uuid/likes')
        .query({ limit: 5 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(400);

      expect(response.body.message).toContain('Validation failed');
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get(`/social/users/${userId}/likes`)
        .query({ limit: 5 })
        .expect(401);
    });
  });
});
