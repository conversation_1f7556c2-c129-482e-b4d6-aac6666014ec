services:
  private-api:
    container_name: letzai-private-api
    image: letzai-api
    build:
      dockerfile: Dockerfile
      context: .
      target: development
    env_file: .env.docker
    environment:
      CONTROLLERS_ENABLE_PRIVATE: true
      CONTROLLERS_ENABLE_PUBLIC: false
      PATH: '/usr/src/app/node_modules/.bin:$PATH' # Add node_modules/.bin to PATH
    expose:
      - 3000
    ports:
      - '3000:3000'
    command: sh -c 'chmod +x /usr/src/app/node_modules/.bin/* && npm run start:dev'
    volumes:
      - .:/usr/src/app
    depends_on:
      - db

  public-api:
    container_name: letzai-public-api
    image: letzai-api
    build:
      dockerfile: Dockerfile
      context: .
      target: development
    env_file: .env.docker
    environment:
      CONTROLLERS_ENABLE_PRIVATE: false
      CONTROLLERS_ENABLE_PUBLIC: true
      PATH: '/usr/src/app/node_modules/.bin:$PATH' # Add node_modules/.bin to PATH
    expose:
      - 3000
    command: sh -c 'chmod +x /usr/src/app/node_modules/.bin/* && npm run start:dev'
    volumes:
      - .:/usr/src/app
    depends_on:
      - db

  db:
    container_name: letzai-db
    image: postgres:14-alpine
    env_file: .env
    expose:
      - 5432
    ports:
      - '5432:5432'
    volumes:
      - letzai-dbdata:/var/lib/postgresql/data

  localstack:
    image: localstack/localstack
    container_name: letzai-localstack
    expose:
      - 4566
      - 4575
    ports:
      - '4566:4566'
      - '4575:4575'
    #      - '${PORT_WEB_UI-8080}:${PORT_WEB_UI-8080}'
    environment:
      - SERVICES=s3,sqs
      - DEFAULT_REGION=us-east-1
      - EDGE_PORT=4566
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - PORT_WEB_UI=${PORT_WEB_UI- }
      - LAMBDA_EXECUTOR=direct
      - AWS_EXECUTION_ENV=True
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - '/var/run/docker.sock:/var/run/docker.sock'

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: letzai-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false # Disable security for local setup
    expose:
      - 9200
    # ports:
    #   - '9200:9200'
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - elk

  kibana:
    image: docker.elastic.co/kibana/kibana:8.9.0
    container_name: letzai-kibana
    depends_on:
      - elasticsearch
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false # Disable security for local setup
    expose:
      - 5601
    # ports:
    #   - '5601:5601'
    networks:
      - elk

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.9.0
    container_name: letzai-filebeat
    user: root
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock # Access Docker socket
      - /var/lib/docker/containers:/var/lib/docker/containers:ro # Access Docker logs
      - ./filebeat.yml:/usr/share/filebeat/filebeat.yml # Custom Filebeat config
    depends_on:
      - elasticsearch
    command: ['--strict.perms=false']
    networks:
      - elk

  image-resizer:
    build: .
    image: letzai-image-resizer
    env_file:
      - .env.docker
    environment:
      - AWS_SQS_QUEUE_URL=${IMAGE_RESIZER_SQS_QUEUE_URL}

networks:
  elk:
    driver: bridge

volumes:
  letzai-dbdata:
  elasticsearch-data:
