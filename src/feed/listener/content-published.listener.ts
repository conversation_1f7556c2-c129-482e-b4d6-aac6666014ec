import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { FeedService } from '../service/feed.service';
import { FeedCacheService } from '../service/feed-cache.service';

/**
 * Listener for content publishing events that affect feeds
 * Handles automatic feed updates when content becomes publicly visible
 */
@Injectable()
export class ContentPublishedListener {
  constructor(
    private readonly feedService: FeedService,
    private readonly feedCacheService: FeedCacheService,
    private readonly logger: Logger,
  ) {}

  /**
   * Handle image published events (when images become public)
   * Automatically adds the published image to the user's feed entries
   */
  @OnEvent('image.published')
  async handleImagePublishedEvent(event: any): Promise<void> {
    try {
      this.logger.debug('Handling image published event for feed update', {
        imageId: event.id,
        userId: event.userId,
        publishedAt: event.publishedAt,
      });

      // Add to user's feed entries
      await this.feedService.addContentToUserFeed(
        event.userId,
        'image',
        event.id,
      );

      // Invalidate discovery feed cache to include new public content
      await this.feedCacheService.invalidateDiscoveryFeed();

      this.logger.log(
        'Image published event processed successfully for feeds',
        {
          imageId: event.id,
          userId: event.userId,
        },
      );
    } catch (error) {
      this.logger.error('Failed to handle image published event for feeds', {
        event,
        error: error.message,
        stack: error.stack,
      });
      // Don't throw error to avoid breaking image publishing process
    }
  }

  /**
   * Handle video published events (when videos become public)
   * Automatically adds the published video to the user's feed entries
   */
  @OnEvent('video.published')
  async handleVideoPublishedEvent(event: any): Promise<void> {
    try {
      this.logger.debug('Handling video published event for feed update', {
        videoId: event.id,
        userId: event.userId,
        publishedAt: event.publishedAt,
      });

      // Add to user's feed entries
      await this.feedService.addContentToUserFeed(
        event.userId,
        'video',
        event.id,
      );

      // Invalidate discovery feed cache to include new public content
      await this.feedCacheService.invalidateDiscoveryFeed();

      this.logger.log(
        'Video published event processed successfully for feeds',
        {
          videoId: event.id,
          userId: event.userId,
        },
      );
    } catch (error) {
      this.logger.error('Failed to handle video published event for feeds', {
        event,
        error: error.message,
        stack: error.stack,
      });
      // Don't throw error to avoid breaking video publishing process
    }
  }
}
