import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsEnum,
  IsArray,
  IsString,
  IsNumber,
  Min,
  Max,
  IsIn,
  IsDateString,
  IsUUID,
} from 'class-validator';
import { SortOrderTransformer } from 'src/core/transformer/sort-order.transformer';

export enum UserFeedPrivacyFilter {
  PUBLIC = 'public',
  PRIVATE = 'private',
  ALL = 'all',
}

export enum UserFeedEntityType {
  ALL = 'all',
  IMAGE = 'image',
  VIDEO = 'video',
}

export enum UserFeedSortBy {
  CREATED_AT = 'createdAt',
  LIKES = 'likes',
  COMMENTS = 'comments',
  REGENERATIONS = 'regenerations',
}

/**
 * User feed request DTO with privacy and model filtering capabilities
 */
export class UserFeedRequestDto {
  @ApiPropertyOptional({
    description: 'Number of items to return',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Cursor for pagination (timestamp from last item)',
    example: '1757849394847',
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cursor?: number;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: UserFeedSortBy,
    default: UserFeedSortBy.CREATED_AT,
    example: 'createdAt',
  })
  @IsOptional()
  @IsEnum(UserFeedSortBy)
  sortBy?: UserFeedSortBy = UserFeedSortBy.CREATED_AT;

  @ApiPropertyOptional({
    description: 'Sort order (case insensitive)',
    enum: ['ASC', 'DESC', 'asc', 'desc'],
    default: 'DESC',
    example: 'DESC',
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC', 'asc', 'desc'], {
    message: 'Sort order must be either ASC, DESC, asc, or desc',
  })
  @SortOrderTransformer()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @ApiPropertyOptional({
    description: 'Privacy filter for content visibility',
    enum: UserFeedPrivacyFilter,
    default: UserFeedPrivacyFilter.PUBLIC,
    example: 'public',
  })
  @IsOptional()
  @IsEnum(UserFeedPrivacyFilter)
  @Transform(({ value }) => value?.toLowerCase())
  privacy?: UserFeedPrivacyFilter = UserFeedPrivacyFilter.PUBLIC;

  @ApiPropertyOptional({
    description: 'Array of model IDs to filter by',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true, message: 'Each model ID must be a string' })
  @Transform(({ value }) => {
    // Filter out empty strings and null/undefined values
    if (Array.isArray(value)) {
      return value.filter(
        (id) => id && typeof id === 'string' && id.trim().length > 0,
      );
    }
    return value;
  })
  modelIds?: string[];

  @ApiPropertyOptional({
    description: 'Entity type filter',
    enum: UserFeedEntityType,
    default: UserFeedEntityType.ALL,
    example: 'all',
  })
  @IsOptional()
  @IsEnum(UserFeedEntityType)
  @Transform(({ value }) => value?.toLowerCase())
  entityType?: UserFeedEntityType = UserFeedEntityType.ALL;

  @ApiPropertyOptional({
    description: 'Include NSFW content',
    default: false,
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeNsfw?: boolean = false;

  @ApiPropertyOptional({
    description: 'Start date for filtering content (ISO 8601 format)',
    example: '2025-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for filtering content (ISO 8601 format)',
    example: '2025-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}
