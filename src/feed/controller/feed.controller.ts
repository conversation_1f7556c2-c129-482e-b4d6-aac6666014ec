import {
  Controller,
  Get,
  Post,
  Put,
  Query,
  Param,
  Body,
  UseGuards,
  HttpStatus,
  HttpCode,
  UsePipes,
  ValidationPipe,
  Request,
  NotFoundException,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';
import { JwtAuthGuard } from '../../auth/guard/jwt-auth.guard';
import { OptionalJwtAuthGuard } from '../../auth/guard/optional-jwt-auth.guard';
import { CurrentUser } from '../../auth/decorator/current-user.decorator';
import { UserEntity } from '../../user/entity/user.entity';
import { FeedService } from '../service/feed.service';
import { FeedPreferencesService } from '../service/feed-preferences.service';
import {
  DiscoveryFeedRequestDto,
  FollowingFeedRequestDto,
  RecommendedFeedRequestDto,
  UpdateFeedPreferencesDto,
  RefreshFeedRequestDto,
} from '../dto/feed-request.dto';
import {
  FeedResponseDto,
  FeedPreferencesResponseDto,
  FeedRefreshResponseDto,
} from '../dto/feed-response.dto';
import { UserFeedRequestDto } from '../dto/user-feed-request.dto';

/**
 * Feed controller handling all feed-related endpoints
 */
@ApiTags('Feed')
@Controller('feed')
export class FeedController {
  constructor(
    private readonly feedService: FeedService,
    private readonly feedPreferencesService: FeedPreferencesService,
    private readonly logger: Logger,
  ) {}

  /**
   * Get discovery feed for global content discovery
   */
  @Get('discovery')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get discovery feed',
    description:
      'Retrieve global discovery feed with trending and popular content',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Discovery feed retrieved successfully',
    type: FeedResponseDto,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items to return (1-100)',
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    type: Number,
    description: 'Pagination cursor (score)',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['image', 'video', 'all'],
    description: 'Entity type filter',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['score', 'recent', 'trending', 'engagement'],
    description: 'Sort order',
  })
  @ApiQuery({
    name: 'minEngagement',
    required: false,
    type: Number,
    description: 'Minimum engagement score',
  })
  @ApiQuery({
    name: 'maxAgeHours',
    required: false,
    type: Number,
    description: 'Maximum content age in hours',
  })
  @ApiQuery({
    name: 'trendingOnly',
    required: false,
    type: Boolean,
    description: 'Show only trending content',
  })
  @UsePipes(new ValidationPipe())
  async getDiscoveryFeed(
    @Request() req,
    @Query() request: DiscoveryFeedRequestDto,
    @Res() res: Response,
  ): Promise<void> {
    const user = req.user;
    this.logger.debug('Discovery feed requested', {
      userId: user?.id,
      request,
      entityType: request.entityType,
      entityTypeType: typeof request.entityType,
    });

    // Disable caching for all discovery feed requests to prevent pagination issues
    // This ensures that cursor-based pagination works correctly
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate, private',
      Pragma: 'no-cache',
      Expires: '0',
      Vary: 'Authorization, Accept, Accept-Encoding',
      'Last-Modified': new Date().toUTCString(),
    });

    const result = await this.feedService.getDiscoveryFeed(
      user?.id || null,
      request,
    );
    res.json(result);
  }

  /**
   * Get following feed for content from followed users
   */
  @Get('following')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get following feed',
    description: 'Retrieve personalized feed with content from followed users',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Following feed retrieved successfully',
    type: FeedResponseDto,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items to return (1-100)',
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    type: Number,
    description: 'Pagination cursor (score)',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['image', 'video', 'all'],
    description: 'Entity type filter',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['score', 'recent', 'trending', 'engagement'],
    description: 'Sort order',
  })
  @ApiQuery({
    name: 'userIds',
    required: false,
    type: [String],
    description: 'Specific user IDs to include',
  })
  @UsePipes(new ValidationPipe())
  async getFollowingFeed(
    @Request() req,
    @Query() request: FollowingFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const user = req.user;
    this.logger.debug('Following feed requested', {
      userId: user.id,
      request,
    });

    return await this.feedService.getFollowingFeed(user.id, request);
  }

  /**
   * Get recommended feed with algorithmic personalization
   */
  @Get('recommended')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get recommended feed',
    description:
      'Retrieve algorithmically personalized feed based on user preferences and behavior',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommended feed retrieved successfully',
    type: FeedResponseDto,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items to return (1-100)',
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    type: Number,
    description: 'Pagination cursor (score)',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['image', 'video', 'all'],
    description: 'Entity type filter',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['score', 'recent', 'trending', 'engagement'],
    description: 'Sort order',
  })
  @ApiQuery({
    name: 'includeSimilar',
    required: false,
    type: Boolean,
    description: 'Include content similar to liked items',
  })
  @UsePipes(new ValidationPipe())
  async getRecommendedFeed(
    @Request() req,
    @Query() request: RecommendedFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const user = req.user;
    this.logger.debug('Recommended feed requested', {
      userId: user.id,
      request,
    });

    return await this.feedService.getRecommendedFeed(user.id, request);
  }

  /**
   * Get user's feed preferences
   */
  @Get('preferences')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get feed preferences',
    description:
      'Retrieve current user feed preferences and personalization settings',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Feed preferences retrieved successfully',
    type: FeedPreferencesResponseDto,
  })
  async getFeedPreferences(
    @CurrentUser() user: UserEntity,
  ): Promise<FeedPreferencesResponseDto> {
    this.logger.debug('Feed preferences requested', {
      userId: user.id,
    });

    return await this.feedPreferencesService.getUserPreferences(user.id);
  }

  /**
   * Update user's feed preferences
   */
  @Put('preferences')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update feed preferences',
    description: 'Update user feed preferences and personalization settings',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Feed preferences updated successfully',
    type: FeedPreferencesResponseDto,
  })
  async updateFeedPreferences(
    @CurrentUser() user: UserEntity,
    @Body() updateDto: UpdateFeedPreferencesDto,
  ): Promise<FeedPreferencesResponseDto> {
    this.logger.debug('Feed preferences update requested', {
      userId: user.id,
      updateDto,
    });

    return await this.feedPreferencesService.updateUserPreferences(
      user.id,
      updateDto,
    );
  }

  /**
   * Refresh user's feed cache
   */
  @Post('refresh')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Refresh feed cache',
    description:
      'Force refresh of user feed cache and regenerate personalized content',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Feed cache refreshed successfully',
    type: FeedRefreshResponseDto,
  })
  @UsePipes(new ValidationPipe())
  async refreshFeed(
    @Request() req,
    @Body() refreshDto: RefreshFeedRequestDto,
  ): Promise<FeedRefreshResponseDto> {
    const user = req.user;
    this.logger.debug('Feed refresh requested', {
      userId: user.id,
      refreshDto,
    });

    return await this.feedService.refreshUserFeed(user.id, refreshDto);
  }

  /**
   * Refresh global feed cache with high-regeneration images
   */
  @Post('refresh-global-cache')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Refresh global feed cache',
    description:
      'Populate global feed cache with images that have high regenerations count',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Global feed cache refreshed successfully',
  })
  async refreshGlobalCache(@Request() req): Promise<any> {
    const user = req.user;
    this.logger.debug('Global feed cache refresh requested', {
      userId: user.id,
    });

    return await this.feedService.refreshGlobalCache();
  }

  /**
   * Get feed for a specific user (public feeds only)
   * Supports both userId (UUID) and username parameters
   */
  @Get('user/:userIdentifier')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get user feed',
    description:
      'Retrieve feed for a specific user by userId (UUID) or username. ' +
      'Supports privacy filtering, model filtering, and content type filtering. ' +
      'Non-owners can only see public content regardless of privacy parameter.',
  })
  @ApiParam({
    name: 'userIdentifier',
    description: 'User ID (UUID format) or username (string format)',
    example: '123e4567-e89b-12d3-a456-************ or john_doe',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User feed retrieved successfully',
    type: FeedResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items to return (1-100)',
    example: 20,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['createdAt', 'likes', 'comments', 'regenerations'],
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort order',
    example: 'DESC',
  })
  @ApiQuery({
    name: 'privacy',
    required: false,
    enum: ['public', 'private', 'all'],
    description:
      'Privacy filter (owners can see private/all, non-owners restricted to public)',
    example: 'public',
  })
  @ApiQuery({
    name: 'modelIds',
    required: false,
    type: [String],
    description: 'Array of model UUIDs to filter by',
    example: ['550e8400-e29b-41d4-a716-************'],
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['image', 'video', 'all'],
    description: 'Entity type filter',
    example: 'all',
  })
  @ApiQuery({
    name: 'includeNsfw',
    required: false,
    type: Boolean,
    description: 'Include NSFW content',
    example: false,
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date for filtering content (ISO 8601 format)',
    example: '2025-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date for filtering content (ISO 8601 format)',
    example: '2025-12-31T23:59:59Z',
  })
  @UsePipes(new ValidationPipe())
  async getUserFeed(
    @Request() req,
    @Param('userIdentifier') userIdentifier: string,
    @Query() request: UserFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const currentUser = req.user;
    this.logger.log('User feed requested', {
      currentUserId: currentUser?.id,
      userIdentifier,
      request,
    });

    try {
      return await this.feedService.getUserFeed(
        userIdentifier,
        currentUser?.id || null,
        request,
      );
    } catch (error) {
      this.logger.error('Error getting user feed', {
        userIdentifier,
        error: error.message,
        stack: error.stack,
      });

      if (error.message?.includes('User not found')) {
        throw new NotFoundException(`User not found: ${userIdentifier}`);
      }

      throw error;
    }
  }

  /**
   * Get feed for a specific board (public boards or member access)
   * Supports both boardId (UUID) and board name parameters
   */
  @Get('board/:boardIdentifier')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get board feed',
    description:
      'Retrieve feed content for a specific board by boardId (UUID) or board name',
  })
  @ApiParam({
    name: 'boardIdentifier',
    description: 'Board ID (UUID format) or board name (string format)',
    example: '123e4567-e89b-12d3-a456-************ or my-board-name',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Board feed retrieved successfully',
    type: FeedResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Board not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Access denied to private board',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items to return (1-100)',
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    type: Number,
    description: 'Pagination cursor (score)',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['image', 'video', 'all'],
    description: 'Entity type filter',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['score', 'recent', 'trending', 'engagement', 'likes', 'createdAt'],
    description: 'Sort criteria',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order (ascending or descending)',
  })
  @UsePipes(new ValidationPipe())
  async getBoardFeed(
    @Request() req,
    @Param('boardIdentifier') boardIdentifier: string,
    @Query() request: DiscoveryFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const currentUser = req.user;
    this.logger.debug('Board feed requested', {
      currentUserId: currentUser?.id,
      boardIdentifier,
      request,
    });

    try {
      return await this.feedService.getBoardFeed(
        boardIdentifier,
        currentUser?.id || null,
        request,
      );
    } catch (error) {
      this.logger.error('Error getting board feed', {
        boardIdentifier,
        error: error.message,
        stack: error.stack,
      });

      if (error.message?.includes('Board not found')) {
        throw new NotFoundException(`Board not found: ${boardIdentifier}`);
      }

      if (error.message?.includes('Authentication required')) {
        throw new NotFoundException(`Board not found: ${boardIdentifier}`); // Don't reveal private board existence
      }

      if (error.message?.includes('Access denied')) {
        throw new NotFoundException(`Board not found: ${boardIdentifier}`); // Don't reveal private board existence
      }

      throw error;
    }
  }

  /**
   * Get trending content
   */
  @Get('trending')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get trending content',
    description: 'Retrieve currently trending content across the platform',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trending content retrieved successfully',
    type: FeedResponseDto,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items to return (1-100)',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['image', 'video', 'all'],
    description: 'Entity type filter',
  })
  @ApiQuery({
    name: 'timeframe',
    required: false,
    enum: ['hour', 'day', 'week'],
    description: 'Trending timeframe',
  })
  @UsePipes(new ValidationPipe())
  async getTrendingContent(
    @Request() req,
    @Query() request: DiscoveryFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const user = req.user;
    this.logger.debug('Trending content requested', {
      userId: user?.id,
      request,
    });

    // Force trending-only filter
    const trendingRequest = {
      ...request,
      trendingOnly: true,
      sortBy: 'trending' as any,
    };

    return await this.feedService.getDiscoveryFeed(
      user?.id || null,
      trendingRequest,
    );
  }
}
