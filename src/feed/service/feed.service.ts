import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Repository,
  SelectQueryBuilder,
  DataSource,
  In,
  Brackets,
} from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import { FeedEntryEntity } from '../entity/feed-entry.entity';
import { GlobalFeedCacheEntity } from '../entity/global-feed-cache.entity';
import { UserFeedPreferencesEntity } from '../entity/user-feed-preferences.entity';
import {
  BaseFeedRequestDto,
  DiscoveryFeedRequestDto,
  FollowingFeedRequestDto,
  RecommendedFeedRequestDto,
  EntityType,
  SortOrder,
  SortOrderDirection,
} from '../dto/feed-request.dto';
import {
  UserFeedPrivacyFilter,
  UserFeedEntityType,
} from '../dto/user-feed-request.dto';
import { FeedResponseDto, FeedItemDto } from '../dto/feed-response.dto';
import { FeedCacheService } from './feed-cache.service';
import { EntityAggregatorService } from '../../shared/service/entity-aggregator.service';
import { UserResponseMapper } from '../../user/service/response-mapper';
import { UserProvider } from '../../user/service/provider';
import { UpscaleResponseMapper } from '../../upscale/service/response-mapper';
import { ImageEditResponseMapper } from '../../image-edit/service/response-mapper';
import { ModelProvider } from '../../model/service/provider';
import { ModelResponseMapper } from '../../model/service/response-mapper';
import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { VideoEntity } from '../../video/entity/video.entity';
import { BoardEntity } from '../../board/entity/board.entity';
import { BoardImageCompletionEntity } from '../../board/entity/board-image-completion.entity';
import { BoardVideoEntity } from '../../board/entity/board-video.entity';
import { BoardProvider } from '../../board/service/provider';
import { BoardUserProvider } from '../../board/service/board-user.provider';
import { VisibilityEnum } from '../../board/enum/board-visibility.enum';

/**
 * Core feed service implementing hybrid feed generation algorithms
 */
@Injectable()
export class FeedService {
  constructor(
    @InjectRepository(FeedEntryEntity)
    private readonly feedEntryRepository: Repository<FeedEntryEntity>,

    @InjectRepository(GlobalFeedCacheEntity)
    private readonly globalFeedCacheRepository: Repository<GlobalFeedCacheEntity>,

    @InjectRepository(UserFeedPreferencesEntity)
    private readonly userPreferencesRepository: Repository<UserFeedPreferencesEntity>,
    @InjectRepository(ImageCompletionEntity)
    private readonly imageCompletionRepository: Repository<ImageCompletionEntity>,

    @InjectRepository(VideoEntity)
    private readonly videoRepository: Repository<VideoEntity>,

    @InjectRepository(BoardImageCompletionEntity)
    private readonly boardImageCompletionRepository: Repository<BoardImageCompletionEntity>,

    @InjectRepository(BoardVideoEntity)
    private readonly boardVideoRepository: Repository<BoardVideoEntity>,

    private readonly feedCacheService: FeedCacheService,
    private readonly entityAggregatorService: EntityAggregatorService,
    private readonly userProvider: UserProvider,
    private readonly userResponseMapper: UserResponseMapper,
    private readonly upscaleResponseMapper: UpscaleResponseMapper,
    private readonly imageEditResponseMapper: ImageEditResponseMapper,
    private readonly modelProvider: ModelProvider,
    private readonly modelResponseMapper: ModelResponseMapper,
    private readonly boardProvider: BoardProvider,
    private readonly boardUserProvider: BoardUserProvider,
    private readonly configService: ConfigService,
    private readonly dataSource: DataSource,
    private readonly logger: Logger,
  ) {}

  /**
   * Get discovery feed for global content discovery
   */
  async getDiscoveryFeed(
    userId: string | null,
    request: DiscoveryFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log('Generating discovery feed', {
        userId,
        request,
        entityType: request.entityType,
        entityTypeValue: JSON.stringify(request.entityType),
        entityTypeType: typeof request.entityType,
      });

      // Check cache first
      const cacheKey = this.buildCacheKey('discovery', userId, request);
      const cachedFeed = await this.feedCacheService.get(cacheKey);

      if (cachedFeed) {
        this.logger.log('Discovery feed cache hit', { userId, cacheKey });
        return this.addMetadata(
          cachedFeed,
          'discovery',
          startTime,
          'hit',
          request,
        );
      }

      // Debug: Check if entity metadata is available
      this.logger.log('Checking GlobalFeedCacheEntity metadata', {
        hasRepository: !!this.globalFeedCacheRepository,
        hasMetadata: !!this.globalFeedCacheRepository?.metadata,
        entityName: this.globalFeedCacheRepository?.metadata?.name,
        tableName: this.globalFeedCacheRepository?.metadata?.tableName,
      });

      // Build query for global feed cache
      const queryBuilder = this.globalFeedCacheRepository
        .createQueryBuilder('gfc')
        .select([
          'gfc.entityType',
          'gfc.entityId',
          'gfc.globalScore',
          'gfc.engagementScore',
          'gfc.recencyScore',
          'gfc.createdAt',
          'gfc.updatedAt',
        ]);

      // Note: isHot field will be fetched later during entity aggregation

      // Apply filters
      this.applyDiscoveryFilters(queryBuilder, request);

      // Apply sorting
      this.applySorting(queryBuilder, request.sortBy, 'gfc', request.sortOrder);

      // Apply pagination
      this.applyPagination(queryBuilder, request, 'gfc', 'globalScore');

      // Execute query - simplified approach
      const feedEntries = await queryBuilder.getMany();

      this.logger.log('DISCOVERY: Feed entries retrieved from cache', {
        entriesFound: feedEntries.length,
        entries: feedEntries.slice(0, 3),
        entityTypes: feedEntries.map((e) => e.entityType).slice(0, 10),
        uniqueEntityTypes: [...new Set(feedEntries.map((e) => e.entityType))],
      });

      // Convert to feed response
      const feedResponse = await this.buildFeedResponse(
        feedEntries.map((entry) => ({
          entityType: entry.entityType,
          entityId: entry.entityId,
          score: entry.globalScore,
          createdAt: entry.createdAt,
          // For now, we'll rely on the filter to ensure only hot items are returned
          // The actual isHot field will be fetched later in the entity aggregation
        })),
        request,
        userId,
      );

      // Cache the result
      await this.feedCacheService.set(cacheKey, feedResponse, 300); // 5 minutes TTL

      return this.addMetadata(
        feedResponse,
        'discovery',
        startTime,
        'miss',
        request,
      );
    } catch (error) {
      this.logger.error('Failed to generate discovery feed', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get following feed for content from followed users
   */
  async getFollowingFeed(
    userId: string,
    request: FollowingFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log('Generating following feed', { userId, request });

      // Validate userId format
      if (!userId || typeof userId !== 'string') {
        throw new Error('Invalid userId provided');
      }

      // Validate UUID format
      if (!this.isValidUUID(userId)) {
        throw new Error(`Invalid userId format: ${userId}`);
      }

      // Check cache first
      const cacheKey = this.buildCacheKey('following', userId, request);
      const cachedFeed = await this.feedCacheService.get(cacheKey);

      if (cachedFeed) {
        this.logger.log('Following feed cache hit', { userId, cacheKey });
        return this.addMetadata(cachedFeed, 'following', startTime, 'hit');
      }

      // Build query for user's feed entries
      const queryBuilder = this.feedEntryRepository
        .createQueryBuilder('fe')
        .select([
          'fe.entityType',
          'fe.entityId',
          'fe.score',
          'fe.createdAt',
          'fe.updatedAt',
        ])
        .where('fe.userId = :userId', { userId });

      // Apply filters
      this.applyFollowingFilters(queryBuilder, request);

      // Apply sorting
      this.applySorting(queryBuilder, request.sortBy, 'fe', request.sortOrder);

      // Apply pagination
      this.applyPagination(queryBuilder, request, 'fe', 'score');

      const feedEntries = await queryBuilder.getMany();

      // Convert to feed response
      const feedResponse = await this.buildFeedResponse(
        feedEntries.map((entry) => ({
          entityType: entry.entityType,
          entityId: entry.entityId,
          score: entry.score,
          createdAt: entry.createdAt,
        })),
        request,
        userId,
      );

      // Cache the result
      await this.feedCacheService.set(cacheKey, feedResponse, 180); // 3 minutes TTL

      return this.addMetadata(feedResponse, 'following', startTime, 'miss');
    } catch (error) {
      this.logger.error('Failed to generate following feed', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get recommended feed with algorithmic personalization
   */
  async getRecommendedFeed(
    userId: string,
    request: RecommendedFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log('Generating recommended feed', { userId, request });

      // Get user preferences
      const preferences = await this.getUserPreferences(userId);

      // Check cache first
      const cacheKey = this.buildCacheKey('recommended', userId, request);
      const cachedFeed = await this.feedCacheService.get(cacheKey);

      if (cachedFeed) {
        this.logger.log('Recommended feed cache hit', { userId, cacheKey });
        return this.addMetadata(cachedFeed, 'recommended', startTime, 'hit');
      }

      // Combine following feed and discovery feed with personalization
      const [followingEntries, discoveryEntries] = await Promise.all([
        this.getPersonalizedFollowingEntries(userId, preferences, request),
        this.getPersonalizedDiscoveryEntries(userId, preferences, request),
      ]);

      // Merge and score entries
      const mergedEntries = this.mergeAndScoreEntries(
        followingEntries,
        discoveryEntries,
        preferences,
        request.algorithmWeights || preferences.algorithmWeights,
      );

      // Convert to feed response
      const feedResponse = await this.buildFeedResponse(
        mergedEntries,
        request,
        userId,
      );

      // Cache the result
      await this.feedCacheService.set(cacheKey, feedResponse, 600); // 10 minutes TTL

      return this.addMetadata(feedResponse, 'recommended', startTime, 'miss');
    } catch (error) {
      this.logger.error('Failed to generate recommended feed', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Apply discovery feed filters
   */
  private applyDiscoveryFilters(
    queryBuilder: SelectQueryBuilder<GlobalFeedCacheEntity>,
    request: DiscoveryFeedRequestDto,
  ): void {
    // Entity type filter - APPLY FIRST for better performance
    const effectiveEntityType = request.entityType || EntityType.ALL;
    this.logger.log('EntityType filter check', {
      requestEntityType: request.entityType,
      effectiveEntityType,
      EntityTypeAll: EntityType.ALL,
      hasEntityType: !!request.entityType,
      isNotAll: effectiveEntityType !== EntityType.ALL,
    });

    if (effectiveEntityType && effectiveEntityType !== EntityType.ALL) {
      this.logger.log('Applying entityType filter', {
        requestEntityType: request.entityType,
        effectiveEntityType,
        filterValue: effectiveEntityType,
      });

      queryBuilder.andWhere('gfc.entityType = :entityType', {
        entityType: effectiveEntityType,
      });

      // TEMPORARY: Log the actual SQL query being generated
      this.logger.log('Generated SQL query', {
        sql: queryBuilder.getQuery(),
        parameters: queryBuilder.getParameters(),
      });
    } else {
      this.logger.log(
        'No entityType filter applied - showing all entity types',
      );
    }

    // 🔒 STRICT PRIVACY FILTERING: Only allow public content in discovery feed
    // This is a critical security requirement - private content must NEVER appear in feeds
    // Apply privacy filtering based on the effective entity type for better performance and accuracy

    if (effectiveEntityType === 'image') {
      // Only join and filter for images when specifically requesting images
      queryBuilder.leftJoin(
        'image_completion',
        'ic_privacy',
        'gfc.entityType = :imageTypePrivacy AND gfc.entityId = ic_privacy.id',
        { imageTypePrivacy: 'image' },
      );
      queryBuilder.andWhere('ic_privacy.privacy = :publicPrivacy', {
        publicPrivacy: 'public',
      });
    } else if (effectiveEntityType === 'video') {
      // Only join and filter for videos when specifically requesting videos
      queryBuilder.leftJoin(
        'video',
        'v_privacy',
        'gfc.entityType = :videoTypePrivacy AND gfc.entityId = v_privacy.id',
        { videoTypePrivacy: 'video' },
      );
      queryBuilder.andWhere('v_privacy.privacy = :publicPrivacy', {
        publicPrivacy: 'public',
      });
    } else {
      // For 'all' entity types, join both tables and use OR condition
      queryBuilder.leftJoin(
        'image_completion',
        'ic_privacy',
        'gfc.entityType = :imageTypePrivacy AND gfc.entityId = ic_privacy.id',
        { imageTypePrivacy: 'image' },
      );
      queryBuilder.leftJoin(
        'video',
        'v_privacy',
        'gfc.entityType = :videoTypePrivacy AND gfc.entityId = v_privacy.id',
        { videoTypePrivacy: 'video' },
      );

      // Enforce public-only privacy for ALL entities (images and videos)
      // Use parentheses to ensure proper grouping with cursor conditions
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where(
            '(gfc.entityType = :imageTypePrivacy2 AND ic_privacy.privacy = :publicPrivacy1)',
            {
              imageTypePrivacy2: 'image',
              publicPrivacy1: 'public',
            },
          ).orWhere(
            '(gfc.entityType = :videoTypePrivacy2 AND v_privacy.privacy = :publicPrivacy2)',
            {
              videoTypePrivacy2: 'video',
              publicPrivacy2: 'public',
            },
          );
        }),
      );
    }

    // Minimum engagement filter
    if (request.minEngagement !== undefined) {
      queryBuilder.andWhere('gfc.engagementScore >= :minEngagement', {
        minEngagement: request.minEngagement,
      });
    }

    // Maximum age filter - filter by original content creation date, not feed cache date
    if (request.maxAgeHours !== undefined) {
      const cutoffDate = new Date(
        Date.now() - request.maxAgeHours * 60 * 60 * 1000,
      );

      // Filter by original content creation date based on entity type
      if (effectiveEntityType === 'image') {
        // Only filter images when specifically requesting images
        queryBuilder.andWhere('ic_privacy.createdAt >= :cutoffDateImage', {
          cutoffDateImage: cutoffDate,
        });
      } else if (effectiveEntityType === 'video') {
        // Only filter videos when specifically requesting videos
        queryBuilder.andWhere('v_privacy.createdAt >= :cutoffDateVideo', {
          cutoffDateVideo: cutoffDate,
        });
      } else {
        // For 'all' entity types, filter both images and videos
        queryBuilder.andWhere(
          new Brackets((qb) => {
            qb.where(
              '(gfc.entityType = :imageTypeAge AND ic_privacy.createdAt >= :cutoffDateImage)',
              {
                imageTypeAge: 'image',
                cutoffDateImage: cutoffDate,
              },
            ).orWhere(
              '(gfc.entityType = :videoTypeAge AND v_privacy.createdAt >= :cutoffDateVideo)',
              {
                videoTypeAge: 'video',
                cutoffDateVideo: cutoffDate,
              },
            );
          }),
        );
      }
    }

    // Trending only filter
    if (request.trendingOnly) {
      queryBuilder.andWhere('gfc.engagementScore > 10');
      queryBuilder.andWhere(
        '(gfc.engagementScore / GREATEST(EXTRACT(EPOCH FROM (NOW() - gfc.createdAt)) / 3600, 1)) > 5.0',
      );
    }

    // Hot content filter - only return items explicitly flagged as hot
    if (request.isHot) {
      // Join with image_completion table to check isHot flag for images
      queryBuilder.leftJoin(
        'image_completion',
        'ic_hot',
        'gfc.entityType = :imageTypeHot AND gfc.entityId = ic_hot.id',
        { imageTypeHot: 'image' },
      );

      // Join with video table to check isHot flag for videos
      queryBuilder.leftJoin(
        'video',
        'v_hot',
        'gfc.entityType = :videoTypeHot AND gfc.entityId = v_hot.id',
        { videoTypeHot: 'video' },
      );

      // Filter for ONLY items explicitly flagged as hot in their respective tables
      queryBuilder.andWhere(
        '(gfc.entityType = :imageTypeHot2 AND ic_hot.isHot = true) OR (gfc.entityType = :videoTypeHot2 AND v_hot.isHot = true)',
        { imageTypeHot2: 'image', videoTypeHot2: 'video' },
      );
    }

    // NSFW content filter - Note: global_feed_cache doesn't have isNsfw column
    // NSFW filtering would need to be done at the entity aggregation level
    // For now, we skip this filter in the discovery feed since it uses cached data
    // TODO: Consider adding isNsfw to global_feed_cache or filtering at aggregation level
    if (!request.includeNsfw) {
      // Skip NSFW filter for cached discovery feed - would require entity joins
      this.logger.log(
        'NSFW filtering skipped for discovery feed (cached data)',
        {
          includeNsfw: request.includeNsfw,
        },
      );
    }

    // Model IDs filter - Apply similar to user feed implementation
    // First check if all model IDs are empty or invalid - if so, treat as no filter
    const nonEmptyModelIds = request.modelIds
      ? request.modelIds.filter(
          (id) => id && typeof id === 'string' && id.trim().length > 0,
        )
      : [];

    if (nonEmptyModelIds.length > 0) {
      // We have at least one non-empty model ID, proceed with filtering
      this.logger.log('Model IDs filtering requested for discovery feed', {
        originalModelIds: request.modelIds,
        modelIdsCount: request.modelIds.length,
      });

      try {
        const validModelIds = this.validateModelIds(request.modelIds);

        this.logger.log('Model IDs validation completed for discovery feed', {
          originalModelIds: request.modelIds,
          validModelIds,
          originalCount: request.modelIds.length,
          validCount: validModelIds.length,
        });

        if (validModelIds.length === 0) {
          // All non-empty model IDs were invalid - apply impossible filter to return 0 results
          queryBuilder.andWhere('1 = 0'); // This will always be false, returning 0 results

          this.logger.warn(
            'All model IDs invalid for discovery feed - applying impossible filter to return 0 results',
            {
              originalModelIds: request.modelIds,
            },
          );
        } else {
          // We need to join with the model association tables to filter by model IDs
          // First, add joins for the image_completion_model junction table
          if (
            effectiveEntityType === 'image' ||
            effectiveEntityType === EntityType.ALL
          ) {
            // Join image_completion if not already joined
            if (
              !queryBuilder.expressionMap.joinAttributes.some(
                (join) => join.alias.name === 'ic_model',
              )
            ) {
              queryBuilder.leftJoin(
                'image_completion',
                'ic_model',
                'gfc.entityType = :imageTypeModel AND gfc.entityId = ic_model.id',
                { imageTypeModel: 'image' },
              );
            }
            // Join the image_completion_model junction table
            queryBuilder.leftJoin(
              'image_completion_model',
              'icm',
              'ic_model.id = icm.image_completion_id',
            );
          }

          if (
            effectiveEntityType === 'video' ||
            effectiveEntityType === EntityType.ALL
          ) {
            // Join video if not already joined
            if (
              !queryBuilder.expressionMap.joinAttributes.some(
                (join) => join.alias.name === 'v_model',
              )
            ) {
              queryBuilder.leftJoin(
                'video',
                'v_model',
                'gfc.entityType = :videoTypeModel AND gfc.entityId = v_model.id',
                { videoTypeModel: 'video' },
              );
            }
            // Join with original image completion for videos (videos inherit models from their original image)
            queryBuilder.leftJoin(
              'image_completion',
              'v_orig_ic',
              'v_model.original_image_completion_id = v_orig_ic.id',
            );
            // Join the image_completion_model junction table for video's original image
            queryBuilder.leftJoin(
              'image_completion_model',
              'v_icm',
              'v_orig_ic.id = v_icm.image_completion_id',
            );
          }

          // Apply the model filtering condition based on entity type
          if (effectiveEntityType === 'image') {
            // Filter only images by model IDs
            queryBuilder.andWhere('icm.model_id IN (:...validModelIds)', {
              validModelIds,
            });
          } else if (effectiveEntityType === 'video') {
            // Filter only videos by model IDs (through their original image)
            queryBuilder.andWhere('v_icm.model_id IN (:...validModelIds)', {
              validModelIds,
            });
          } else {
            // Filter both images and videos by model IDs
            queryBuilder.andWhere(
              new Brackets((qb) => {
                qb.where(
                  '(gfc.entityType = :imageTypeModelFilter AND icm.model_id IN (:...validModelIds))',
                  {
                    imageTypeModelFilter: 'image',
                    validModelIds,
                  },
                ).orWhere(
                  '(gfc.entityType = :videoTypeModelFilter AND v_icm.model_id IN (:...validModelIds))',
                  {
                    videoTypeModelFilter: 'video',
                    validModelIds,
                  },
                );
              }),
            );
          }

          // Add DISTINCT to avoid duplicates from joins
          queryBuilder.distinct(true);

          this.logger.log(
            'Database-level model filtering applied to discovery feed',
            {
              validModelIds,
              modelIdsCount: validModelIds.length,
              effectiveEntityType,
            },
          );
        }
      } catch (error) {
        this.logger.error('Error applying model IDs filter to discovery feed', {
          modelIds: request.modelIds,
          error: error.message,
          stack: error.stack,
        });
        // Don't throw the error, just log it and continue without model filtering
        // This ensures the API doesn't break for invalid model IDs
      }
    }

    // Edited images filter - requires metadata not available in cache
    if (request.editedImages !== undefined) {
      // Note: global_feed_cache doesn't have metadata column for edited images
      // This filter would need to be implemented at the entity aggregation level
      // TODO: Consider adding edited flag to global_feed_cache or filtering at aggregation level
      this.logger.log(
        'Edited images filter skipped for discovery feed (cached data)',
        {
          editedImages: request.editedImages,
        },
      );
    }

    // Start date filter
    if (request.startDate) {
      const startDate = new Date(request.startDate);
      queryBuilder.andWhere('gfc.createdAt >= :startDate', { startDate });
    }

    // End date filter
    if (request.endDate) {
      const endDate = new Date(request.endDate);
      queryBuilder.andWhere('gfc.createdAt <= :endDate', { endDate });
    }
  }

  /**
   * Apply following feed filters
   */
  private applyFollowingFilters(
    queryBuilder: SelectQueryBuilder<FeedEntryEntity>,
    request: FollowingFeedRequestDto,
  ): void {
    // 🔒 STRICT PRIVACY FILTERING: Only allow public content in following feed
    // This is a critical security requirement - private content must NEVER appear in feeds
    // Join with entity tables to enforce privacy at database level
    queryBuilder.leftJoin(
      'image_completion',
      'ic_privacy_follow',
      'fe.entityType = :imageTypePrivacyFollow AND fe.entityId = ic_privacy_follow.id',
      { imageTypePrivacyFollow: 'image' },
    );
    queryBuilder.leftJoin(
      'video',
      'v_privacy_follow',
      'fe.entityType = :videoTypePrivacyFollow AND fe.entityId = v_privacy_follow.id',
      { videoTypePrivacyFollow: 'video' },
    );

    // Enforce public-only privacy for ALL entities (images and videos)
    queryBuilder.andWhere(
      '(fe.entityType = :imageTypePrivacyFollow2 AND ic_privacy_follow.privacy = :publicPrivacyFollow1) OR (fe.entityType = :videoTypePrivacyFollow2 AND v_privacy_follow.privacy = :publicPrivacyFollow2)',
      {
        imageTypePrivacyFollow2: 'image',
        videoTypePrivacyFollow2: 'video',
        publicPrivacyFollow1: 'public',
        publicPrivacyFollow2: 'public',
      },
    );

    // Entity type filter
    if (request.entityType && request.entityType !== EntityType.ALL) {
      queryBuilder.andWhere('fe.entityType = :entityType', {
        entityType: request.entityType,
      });
    }

    // Specific user IDs filter
    if (request.userIds && request.userIds.length > 0) {
      queryBuilder.andWhere(
        'fe.entityId IN (SELECT id FROM image_completions WHERE user_id IN (:...userIds) UNION SELECT id FROM videos WHERE user_id IN (:...userIds))',
        {
          userIds: request.userIds,
        },
      );
    }

    // Exclude expired entries unless explicitly requested
    if (!request.includeExpired) {
      queryBuilder.andWhere('(fe.expiresAt IS NULL OR fe.expiresAt > NOW())');
    }
  }

  /**
   * Check if user is a member of the board
   */
  private async isBoardMember(
    userId: string,
    boardId: string,
  ): Promise<boolean> {
    try {
      const boardUser = await this.dataSource.query(
        'SELECT 1 FROM board_user WHERE user_id = $1 AND board_id = $2 AND deleted_at IS NULL',
        [userId, boardId],
      );
      return boardUser.length > 0;
    } catch (error) {
      this.logger.error('Error checking board membership', {
        userId,
        boardId,
        error,
      });
      return false;
    }
  }

  /**
   * Apply post-fetch filters to unified entities
   * This handles filters that require actual entity data (privacy, modelIds, etc.)
   */
  private applyPostFetchFilters(
    entities: any[],
    request: any,
    userId: string | null,
  ): any[] {
    this.logger.log('applyPostFetchFilters called', {
      entitiesCount: entities.length,
      requestKeys: Object.keys(request || {}),
      hasModelIds: !!request.modelIds?.length,
      modelIds: request.modelIds,
      sampleEntityKeys:
        entities.length > 0 ? Object.keys(entities[0] || {}) : [],
      sampleEntityType: entities.length > 0 ? entities[0]?.entityType : null,
    });

    let filteredEntities = [...entities];

    // Privacy filter - now handled at database level for security
    // Database-level filtering ensures private content never reaches this point
    // This provides defense-in-depth security for privacy compliance
    if (request.privacy !== undefined && request.privacy !== 'all') {
      // If specific privacy level requested, filter for it
      // Note: Database already enforces public-only, so this mainly handles edge cases
      filteredEntities = filteredEntities.filter((entity) => {
        return entity.privacy === request.privacy;
      });
    }
    // No else clause needed - database-level filtering already enforces public-only content

    // NSFW filter
    if (request.includeNsfw == false) {
      filteredEntities = filteredEntities.filter((entity) => {
        return !entity.isUnsafe;
      });
    }

    // Model IDs filter is now applied at database level in getUserFeedEntries and getUserContentEntries
    // No application-level filtering needed for model IDs
    return filteredEntities;
  }

  /**
   * Apply sorting to query builder
   */
  private applySorting(
    queryBuilder: SelectQueryBuilder<any>,
    sortBy: SortOrder,
    alias: string,
    sortOrder: 'ASC' | 'DESC' | 'RANDOM' = 'DESC',
  ): void {
    // Check if DISTINCT is being used (when model filtering is applied)
    const isDistinct =
      (queryBuilder as any).expressionMap?.selectDistinct === true;

    switch (sortBy) {
      case SortOrder.SCORE:
        queryBuilder.orderBy(
          `${alias}.${alias === 'gfc' ? 'globalScore' : 'score'}`,
          sortOrder.toUpperCase() as 'ASC' | 'DESC',
        );
        queryBuilder.addOrderBy(`${alias}.createdAt`, 'DESC');
        break;
      case SortOrder.RECENT:
        // For recent sorting, we need to join with entity tables to get actual creation date
        if (alias === 'gfc') {
          // Join with image_completion table for images
          queryBuilder.leftJoin(
            'image_completion',
            'ic_recent',
            `${alias}.entityType = :imageType AND ${alias}.entityId = ic_recent.id`,
            { imageType: 'image' },
          );
          // Join with video table for videos
          queryBuilder.leftJoin(
            'video',
            'v_recent',
            `${alias}.entityType = :videoType AND ${alias}.entityId = v_recent.id`,
            { videoType: 'video' },
          );

          // When using DISTINCT, we need to add the ORDER BY expression to SELECT
          const sortExpression = `COALESCE(ic_recent.created_at, v_recent.created_at, ${alias}.created_at)`;
          if (isDistinct) {
            queryBuilder.addSelect(sortExpression, 'sort_order_field');
          }

          // Sort by actual entity creation date, fallback to cache date
          queryBuilder.orderBy(
            sortExpression,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        } else {
          // Fallback to default sorting for non-cache queries
          queryBuilder.orderBy(
            `${alias}.createdAt`,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        }
        break;
      case SortOrder.TRENDING:
        if (alias === 'gfc') {
          const trendingExpression = `(${alias}.engagementScore / GREATEST(EXTRACT(EPOCH FROM (NOW() - ${alias}.createdAt)) / 3600, 1))`;

          // When using DISTINCT, we need to add the ORDER BY expression to SELECT
          if (isDistinct) {
            queryBuilder.addSelect(trendingExpression, 'trending_score');
          }

          queryBuilder.orderBy(
            trendingExpression,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        } else {
          queryBuilder.orderBy(
            `${alias}.score`,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        }
        break;
      case SortOrder.ENGAGEMENT:
        if (alias === 'gfc') {
          queryBuilder.orderBy(
            `${alias}.engagementScore`,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        } else {
          queryBuilder.orderBy(
            `${alias}.score`,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        }
        break;
      case SortOrder.CREATED_AT:
        // For createdAt sorting, we need to join with entity tables to get actual creation date
        if (alias === 'gfc') {
          // Join with image_completion table for images
          queryBuilder.leftJoin(
            'image_completion',
            'ic_date',
            `${alias}.entityType = :imageType AND ${alias}.entityId = ic_date.id`,
            { imageType: 'image' },
          );
          // Join with video table for videos
          queryBuilder.leftJoin(
            'video',
            'v_date',
            `${alias}.entityType = :videoType AND ${alias}.entityId = v_date.id`,
            { videoType: 'video' },
          );

          // When using DISTINCT, we need to add the ORDER BY expression to SELECT
          const dateExpression = `COALESCE(ic_date.created_at, v_date.created_at, ${alias}.created_at)`;
          if (isDistinct) {
            queryBuilder.addSelect(dateExpression, 'created_at_sort');
          }

          // Sort by actual entity creation date, fallback to cache date
          queryBuilder.orderBy(
            dateExpression,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        } else {
          // Fallback to default sorting for non-cache queries
          queryBuilder.orderBy(
            `${alias}.createdAt`,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        }
        break;
      case SortOrder.RANDOM:
        queryBuilder.orderBy('RANDOM()');
        break;
      case SortOrder.REGENERATIONS:
        // For regenerations sorting, we need to join with image_completion table
        if (alias === 'gfc') {
          // Filter to only show images since videos don't have regenerations
          queryBuilder.andWhere(`${alias}.entityType = :imageTypeRegenFilter`, {
            imageTypeRegenFilter: 'image',
          });

          // Join with image_completion table to get regenerations count
          queryBuilder.leftJoin(
            'image_completion',
            'ic_regen',
            `${alias}.entityId = ic_regen.id AND ic_regen.deleted_at IS NULL`,
          );

          // When using DISTINCT, we need to add the ORDER BY expression to SELECT
          const regenerationsExpression = 'COALESCE(ic_regen.regenerations, 0)';
          if (isDistinct) {
            queryBuilder.addSelect(
              regenerationsExpression,
              'regenerations_sort',
            );
          }

          // Sort by regenerations count, treating NULL as 0
          queryBuilder.orderBy(
            regenerationsExpression,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );

          // Secondary sort by global score for ties
          queryBuilder.addOrderBy(
            `${alias}.${alias === 'gfc' ? 'globalScore' : 'score'}`,
            'DESC',
          );
        } else {
          // Fallback to default sorting for non-cache queries
          queryBuilder.orderBy(
            `${alias}.regenerations`,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        }
        break;
      case SortOrder.LIKES:
        // For likes sorting, we need to join with entity_like table to count likes
        if (alias === 'gfc') {
          // Join with entity_like table to count likes for each entity
          queryBuilder.leftJoin(
            'entity_like',
            'el_likes',
            `${alias}.entityType = el_likes.entity_type AND ${alias}.entityId = el_likes.entity_id AND el_likes.deleted_at IS NULL`,
          );

          // Group by all selected fields to enable COUNT aggregation
          queryBuilder.groupBy(`${alias}.id`);
          queryBuilder.addGroupBy(`${alias}.entityType`);
          queryBuilder.addGroupBy(`${alias}.entityId`);
          queryBuilder.addGroupBy(
            `${alias}.${alias === 'gfc' ? 'globalScore' : 'score'}`,
          );
          if (alias === 'gfc') {
            queryBuilder.addGroupBy(`${alias}.engagementScore`);
            queryBuilder.addGroupBy(`${alias}.recencyScore`);
          }
          queryBuilder.addGroupBy(`${alias}.createdAt`);
          queryBuilder.addGroupBy(`${alias}.updatedAt`);

          // Sort by likes count, treating NULL as 0
          queryBuilder.orderBy(
            'COUNT(el_likes.id)',
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );

          // Secondary sort by global score for ties
          queryBuilder.addOrderBy(
            `${alias}.${alias === 'gfc' ? 'globalScore' : 'score'}`,
            'DESC',
          );
        } else {
          // Fallback to default sorting for non-cache queries
          queryBuilder.orderBy(
            `${alias}.score`,
            sortOrder.toUpperCase() as 'ASC' | 'DESC',
          );
        }
        break;
      default:
        queryBuilder.orderBy(
          `${alias}.${alias === 'gfc' ? 'globalScore' : 'score'}`,
          sortOrder.toUpperCase() as 'ASC' | 'DESC',
        );
    }
  }

  /**
   * Apply pagination to query builder
   */
  private applyPagination(
    queryBuilder: SelectQueryBuilder<any>,
    request: BaseFeedRequestDto,
    alias = 'gfc',
    scoreColumn = 'globalScore',
  ): void {
    // Dynamic cursor-based pagination based on sortBy field
    const sortBy = request.sortBy || SortOrder.SCORE;
    const sortOrder = request.sortOrder || 'DESC';

    // Determine cursor operator based on sort order
    // ASC: Use > (greater than) to get items after cursor
    // DESC: Use < (less than) to get items before cursor
    const cursorOperator = sortOrder.toUpperCase() === 'ASC' ? '>' : '<';

    if (request.cursor !== undefined) {
      // Apply cursor filter based on the sorting field
      switch (sortBy) {
        case SortOrder.CREATED_AT:
        case SortOrder.RECENT:
          // For date-based sorting, use cursorTimestamp if available, otherwise treat cursor as timestamp
          if (request.cursorTimestamp) {
            queryBuilder.andWhere(
              `${alias}.createdAt ${cursorOperator} :cursorTimestamp`,
              {
                cursorTimestamp: request.cursorTimestamp,
              },
            );
          } else {
            // Treat cursor as timestamp (Unix timestamp or ISO string)
            const cursorDate = new Date(request.cursor);
            if (!isNaN(cursorDate.getTime())) {
              queryBuilder.andWhere(
                `${alias}.createdAt ${cursorOperator} :cursorDate`,
                {
                  cursorDate: cursorDate,
                },
              );
            }
          }
          break;
        case SortOrder.SCORE:
        case SortOrder.TRENDING:
        case SortOrder.ENGAGEMENT:
        case SortOrder.LIKES:
        case SortOrder.REGENERATIONS:
        default:
          // For score-based sorting, use score as primary cursor and timestamp as tie-breaker
          if (request.cursorTimestamp) {
            // Use both score and timestamp for precise pagination
            // For DESC order: (score < cursor) OR (score = cursor AND createdAt < cursorTimestamp)
            // For ASC order: (score > cursor) OR (score = cursor AND createdAt > cursorTimestamp)
            queryBuilder.andWhere(
              `(${alias}.${scoreColumn} ${cursorOperator} :cursor OR (${alias}.${scoreColumn} = :cursor AND ${alias}.createdAt ${cursorOperator} :cursorTimestamp))`,
              {
                cursor: request.cursor,
                cursorTimestamp: request.cursorTimestamp,
              },
            );
          } else {
            // Without timestamp, we need to be more careful with score-only filtering
            // For items with the same score, we might miss some or get duplicates
            // Use strict inequality to avoid including the cursor item itself
            queryBuilder.andWhere(
              `${alias}.${scoreColumn} ${cursorOperator} :cursor`,
              {
                cursor: request.cursor,
              },
            );
          }
          break;
      }
    }

    // Additional timestamp cursor for hybrid pagination
    if (request.cursorTimestamp && !request.cursor) {
      queryBuilder.andWhere(
        `${alias}.createdAt ${cursorOperator} :cursorTimestamp`,
        {
          cursorTimestamp: request.cursorTimestamp,
        },
      );
    }

    queryBuilder.limit(request.limit || 20);
  }

  /**
   * Get user preferences or create default ones
   */
  private async getUserPreferences(
    userId: string,
  ): Promise<UserFeedPreferencesEntity> {
    let preferences = await this.userPreferencesRepository.findOne({
      where: { userId },
    });

    if (!preferences) {
      preferences = UserFeedPreferencesEntity.createDefault(userId);
      await this.userPreferencesRepository.save(preferences);
    }

    return preferences;
  }

  /**
   * Get personalized following entries
   */
  private async getPersonalizedFollowingEntries(
    userId: string,
    preferences: UserFeedPreferencesEntity,
    request: RecommendedFeedRequestDto,
  ): Promise<any[]> {
    const queryBuilder = this.feedEntryRepository
      .createQueryBuilder('fe')
      .select(['fe.entityType', 'fe.entityId', 'fe.score', 'fe.createdAt'])
      .where('fe.userId = :userId', { userId })
      .orderBy('fe.score', 'DESC')
      .limit(Math.floor((request.limit || 20) * 0.7)); // 70% from following

    if (request.entityType && request.entityType !== EntityType.ALL) {
      queryBuilder.andWhere('fe.entityType = :entityType', {
        entityType: request.entityType,
      });
    }

    return await queryBuilder.getMany();
  }

  /**
   * Get personalized discovery entries
   */
  private async getPersonalizedDiscoveryEntries(
    userId: string,
    preferences: UserFeedPreferencesEntity,
    request: RecommendedFeedRequestDto,
  ): Promise<any[]> {
    const queryBuilder = this.globalFeedCacheRepository
      .createQueryBuilder('gfc')
      .select([
        'gfc.entityType',
        'gfc.entityId',
        'gfc.globalScore',
        'gfc.createdAt',
      ])
      .orderBy('gfc.globalScore', 'DESC')
      .limit(Math.floor((request.limit || 20) * 0.3)); // 30% from discovery

    if (request.entityType && request.entityType !== EntityType.ALL) {
      queryBuilder.andWhere('gfc.entityType = :entityType', {
        entityType: request.entityType,
      });
    }

    // Apply user preferences
    if (preferences.preferredEntityTypes.length > 0) {
      queryBuilder.andWhere('gfc.entityType IN (:...preferredTypes)', {
        preferredTypes: preferences.preferredEntityTypes,
      });
    }

    const entries = await queryBuilder.getMany();

    return entries.map((entry) => ({
      entityType: entry.entityType,
      entityId: entry.entityId,
      score: entry.globalScore,
      createdAt: entry.createdAt,
    }));
  }

  /**
   * Merge and score entries from different sources
   */
  private mergeAndScoreEntries(
    followingEntries: any[],
    discoveryEntries: any[],
    preferences: UserFeedPreferencesEntity,
    _algorithmWeights: Record<string, number>,
  ): any[] {
    const allEntries = [
      ...followingEntries.map((entry) => ({ ...entry, source: 'following' })),
      ...discoveryEntries.map((entry) => ({ ...entry, source: 'discovery' })),
    ];

    // Remove duplicates
    const uniqueEntries = allEntries.filter(
      (entry, index, self) =>
        index ===
        self.findIndex(
          (e) =>
            e.entityType === entry.entityType && e.entityId === entry.entityId,
        ),
    );

    // Apply personalized scoring
    return uniqueEntries
      .map((entry) => {
        const personalizedScore = preferences.calculatePersonalizedScore(
          entry.score,
          entry.entityType,
          {},
          entry.source === 'following',
        );

        return {
          ...entry,
          score: personalizedScore,
        };
      })
      .sort((a, b) => b.score - a.score);
  }

  /**
   * Build feed response from entries with hybrid format (original + new properties)
   * ENHANCED: Includes comprehensive ownership validation to prevent data isolation issues
   */
  private async buildFeedResponse(
    entries: any[],
    request: any, // Use any to support both BaseFeedRequestDto and UserFeedRequestDto
    userId: string | null,
    hasMoreItems?: boolean,
    expectedOwnerId?: string, // NEW: Expected owner for user feeds
  ): Promise<FeedResponseDto> {
    this.logger.log('Building feed response', {
      entriesCount: entries.length,
      userId,
      expectedOwnerId,
      request: {
        limit: request.limit,
        sortBy: request.sortBy,
        modelIds: request.modelIds,
      },
      hasModelIds: !!request.modelIds?.length,
      modelIdsCount: request.modelIds?.length || 0,
    });

    // Fetch real entity data using EntityAggregatorService
    const entityRequests = entries.map((entry) => ({
      entityType: entry.entityType,
      entityId: entry.entityId,
    }));

    const unifiedEntities =
      await this.entityAggregatorService.getUnifiedEntities(entityRequests);

    // CRITICAL SECURITY: Validate ownership if this is a user feed
    if (expectedOwnerId) {
      const ownershipValidationResults =
        await this.validateUnifiedEntitiesOwnership(
          unifiedEntities,
          expectedOwnerId,
          'response_building',
        );

      if (ownershipValidationResults.violationCount > 0) {
        this.logger.error(
          'CRITICAL: Ownership violations detected in feed response building',
          {
            userId,
            expectedOwnerId,
            totalEntities: unifiedEntities.length,
            violationCount: ownershipValidationResults.violationCount,
            violations: ownershipValidationResults.violations.slice(0, 5), // Log first 5 violations
          },
        );

        // Filter out violating entities to prevent data leakage
        const validEntities = ownershipValidationResults.validEntities;
        this.logger.warn(
          'Filtered out ownership violations from feed response',
          {
            originalCount: unifiedEntities.length,
            filteredCount: validEntities.length,
            removedCount: unifiedEntities.length - validEntities.length,
          },
        );

        // Update entries to match filtered entities
        const validEntityIds = new Set(validEntities.map((e) => e.entityId));
        entries = entries.filter((entry) => validEntityIds.has(entry.entityId));
      }
    }

    // Apply post-fetch filtering based on request parameters
    const filteredEntities = this.applyPostFetchFilters(
      unifiedEntities,
      request,
      userId,
    );

    // Create a map for quick lookup
    const entityMap = new Map();
    filteredEntities.forEach((entity) => {
      const key = `${entity.entityType}-${entity.entityId}`;
      entityMap.set(key, entity);
    });

    // Build feed items with hybrid format (original structure + new properties)
    const itemPromises = entries.map(async (entry) => {
      const entityKey = `${entry.entityType}-${entry.entityId}`;
      const unifiedEntity = entityMap.get(entityKey);

      if (!unifiedEntity) {
        this.logger.warn('Entity not found for feed entry', {
          entityType: entry.entityType,
          entityId: entry.entityId,
          entityKey,
        });
        return null;
      }

      const feedItem: FeedItemDto = {
        type: entry.entityType,
        score: entry.score, // NEW: feed-specific score
        likes: unifiedEntity.engagement.likes || 0, // Top-level likes for sorting
        comments: unifiedEntity.engagement.comments || 0, // Top-level comments for sorting
        entityId: entry.entityId, // Legacy compatibility
        entityType: entry.entityType, // Legacy compatibility
      };

      // Create full entity object in original format
      if (entry.entityType === 'image') {
        feedItem.imageCompletion = await this.buildImageCompletionDto(
          unifiedEntity,
          userId,
          request,
        );
      } else if (entry.entityType === 'video') {
        feedItem.video = await this.buildVideoDto(
          unifiedEntity,
          userId,
          request,
        );
      }

      return feedItem;
    });

    const items: FeedItemDto[] = (await Promise.all(itemPromises)).filter(
      (item) => item !== null,
    ) as FeedItemDto[];

    // Use provided hasMoreItems parameter if available, otherwise use default logic
    const hasMore =
      hasMoreItems !== undefined
        ? hasMoreItems
        : entries.length === (request.limit || 20);
    const lastEntry = entries[entries.length - 1];

    // Generate cursor based on sorting field
    let nextCursor: number | undefined;
    let nextCursorTimestamp: string | undefined;

    if (hasMore && items.length > 0) {
      const lastItem = items[items.length - 1];
      const sortBy = String(request.sortBy || SortOrder.SCORE);

      // Handle different sorting types
      if (sortBy === 'createdAt' || sortBy === 'recent') {
        // For date-based sorting, use timestamp as cursor
        const lastItemCreatedAt =
          lastItem.type === 'image'
            ? lastItem.imageCompletion?.createdAt
            : lastItem.video?.createdAt;
        nextCursorTimestamp = lastItemCreatedAt;
        nextCursor = lastItemCreatedAt
          ? new Date(lastItemCreatedAt).getTime()
          : undefined;
      } else {
        // For score-based sorting (score, trending, engagement, likes, regenerations), use score as cursor
        // and timestamp as secondary cursor for tie-breaking
        nextCursor = lastItem.score ? Number(lastItem.score) : undefined;
        const lastItemCreatedAt =
          lastItem.type === 'image'
            ? lastItem.imageCompletion?.createdAt
            : lastItem.video?.createdAt;
        nextCursorTimestamp = lastItemCreatedAt;
      }
    }

    return {
      items,
      pagination: {
        count: items.length,
        hasMore,
        nextCursor,
        nextCursorTimestamp,
      },
      metadata: {
        feedType: 'unknown',
        generatedAt: new Date().toISOString(),
      },
    };
  }

  /**
   * Build PublicUserDto from owner data
   */
  private async buildPublicUserDto(ownerId: string): Promise<any> {
    try {
      const user = await this.userProvider.findOne(ownerId);
      if (!user) {
        // Fallback to basic user info if user not found
        return {
          id: ownerId,
          username: 'unknown',
          name: 'Unknown User',
          profilePicture: null,
          description: null,
          website: null,
          socialMediaAccounts: null,
          imagesGenerated: 0,
          imagesAvailable: 0,
          modelsAvailable: 0,
          followersCount: 0,
          followingCount: 0,
          isVerified: false,
        };
      }
      return this.userResponseMapper.mapPublic(user);
    } catch (error) {
      this.logger.warn('Failed to fetch user data for feed', {
        ownerId,
        error: error.message,
      });
      // Return fallback user data
      return {
        id: ownerId,
        username: 'unknown',
        name: 'Unknown User',
        profilePicture: null,
        description: null,
        website: null,
        socialMediaAccounts: null,
        imagesGenerated: 0,
        imagesAvailable: 0,
        modelsAvailable: 0,
        followersCount: 0,
        followingCount: 0,
        isVerified: false,
      };
    }
  }

  /**
   * Build ImageCompletionDto from unified entity data
   * Enhanced to match legacy ImageCompletionResponseMapper.map() output
   */
  private async buildImageCompletionDto(
    unifiedEntity: any,
    userId: string | null,
    request?: any,
  ): Promise<any> {
    this.logger.debug('buildImageCompletionDto called', {
      entityId: unifiedEntity.entityId,
      userId,
    });

    const user = await this.buildPublicUserDto(unifiedEntity.owner.id);

    // Fetch the full image completion entity with all relations for missing properties
    let fullImageEntity: any = null;
    try {
      // Apply model filtering if model IDs are specified in the request
      const modelIds = request?.modelIds;
      const hasModelFilter = modelIds && modelIds.length > 0;

      if (hasModelFilter) {
        // Validate model IDs to prevent PostgreSQL UUID errors
        const validModelIds = this.validateModelIds(modelIds);

        // Use query builder to apply model filtering
        fullImageEntity = await this.imageCompletionRepository
          .createQueryBuilder('ic')
          .leftJoinAndSelect('ic.originalImageCompletionRelations', 'oicr')
          .leftJoinAndSelect('oicr.originalImageCompletion', 'oic')
          .leftJoinAndSelect('oic.user', 'oic_user')
          .leftJoinAndSelect('ic.imageEditImageCompletion', 'ieic')
          .leftJoinAndSelect('ieic.imageEdit', 'ie')
          .leftJoinAndSelect('ic.upscales', 'upscales')
          .leftJoinAndSelect(
            'ic.models',
            'models',
            'models.modelId IN (:...validModelIds)',
            { validModelIds },
          )
          .leftJoinAndSelect('models.model', 'model')
          .where('ic.id = :id', { id: unifiedEntity.entityId })
          .andWhere('ic.deletedAt IS NULL')
          .getOne();

        console.log('🔍 APPLIED MODEL FILTERING TO ENTITY FETCH', {
          entityId: unifiedEntity.entityId,
          requestedModelIds: modelIds,
          validModelIds: validModelIds,
          foundModelsCount: fullImageEntity?.models?.length || 0,
          foundModelIds: fullImageEntity?.models?.map((m) => m.modelId) || [],
        });
      } else {
        // No model filtering - fetch all models as before
        fullImageEntity = await this.imageCompletionRepository.findOne({
          where: { id: unifiedEntity.entityId, deletedAt: null },
          relations: [
            'originalImageCompletionRelations',
            'originalImageCompletionRelations.originalImageCompletion',
            'originalImageCompletionRelations.originalImageCompletion.user',
            'imageEditImageCompletion',
            'imageEditImageCompletion.imageEdit',
            'upscales',
            'models',
            'models.model',
          ],
        });
      }
    } catch (error) {
      this.logger.warn('Failed to fetch full image completion entity', {
        entityId: unifiedEntity.entityId,
        error: error.message,
      });
    }

    // Fetch actual regenerations count, isHot flag, and imagePaths from image_completion table
    let regenerations = 0;
    let isHot = false;
    let imagePaths: string[] = [];
    try {
      const result = await this.imageCompletionRepository
        .createQueryBuilder('ic')
        .select('ic.regenerations', 'regenerations')
        .addSelect('ic.isHot', 'isHot')
        .addSelect('ic.imagePaths', 'imagePaths')
        .where('ic.id = :id', { id: unifiedEntity.entityId })
        .andWhere('ic.deletedAt IS NULL')
        .getRawOne();

      regenerations = result?.regenerations || 0;
      isHot = result?.isHot || false;
      imagePaths = result?.imagePaths || [];

      this.logger.log('Fetched image completion data', {
        entityId: unifiedEntity.entityId,
        regenerations,
        isHot,
        imagePathsCount: imagePaths.length,
        resultFound: !!result,
      });
    } catch (error) {
      this.logger.warn('Failed to fetch image completion data', {
        entityId: unifiedEntity.entityId,
        error: error.message,
      });
    }

    return {
      id: unifiedEntity.entityId,
      user: user,
      regeneratedFromId: null, // Would need to be included in unified entity
      prompt: unifiedEntity.title,
      status: 'ready', // Assuming ready since it's in the feed
      statusDetail: null,
      progress: 100,
      previewImage: null,
      username: unifiedEntity.owner.username,
      queue: null,
      isUserVerified: false, // Would need to be fetched from user data
      privacy: unifiedEntity.privacy || 'public',
      likes: unifiedEntity.engagement.likes,
      comments: unifiedEntity.engagement.comments,
      regenerations: regenerations, // Now fetching actual value from database
      generationSettings: unifiedEntity.metadata?.generationSettings || {},
      systemVersion: null, // Would need to be included in unified entity
      reports: 0, // Would need to be included in unified entity
      hasWatermark: false, // Would need to be included in unified entity
      isNsfw: unifiedEntity.isUnsafe || false,
      webhookUrl: null,
      isUnsafe: unifiedEntity.isUnsafe || false,
      isHot: isHot, // Now fetching actual isHot value from database
      hidePrompt: false, // Would need to be included in unified entity
      blockedAt: null,
      createdAt: unifiedEntity.createdAt,
      updatedAt: unifiedEntity.updatedAt,

      // Legacy compatibility fields - now properly populated
      originalImageCompletions: await this.buildOriginalImageCompletions(
        fullImageEntity,
        userId,
      ),
      originalImageCompletionIds:
        this.buildOriginalImageCompletionIds(fullImageEntity),
      imageEdit: await this.buildImageEdit(fullImageEntity),
      liked: userId
        ? await this.checkUserLiked(unifiedEntity.entityId, userId)
        : false,
      isBookmarked: userId
        ? await this.checkUserBookmarked(unifiedEntity.entityId, userId)
        : false,

      // Image versions and URLs - critical for display (now includes upscales)
      imageVersions: await this.generateImageVersionsWithUpscales(
        imagePaths,
        fullImageEntity,
      ),

      // Models used for generation - fetch from the models relation
      models: await this.buildModelsArray(fullImageEntity),

      // Debug: Add modelId field for easier access in tests
      // When model filtering is applied, ensure we get the correct model ID
      modelId: await this.getImageModelId(fullImageEntity, request?.modelIds),

      // Additional metadata
      tags: unifiedEntity.tags || [],
      metadata: unifiedEntity.metadata || {},
    };
  }

  /**
   * Build VideoDto from unified entity data (matches VideoResponseMapper structure)
   */
  private async buildVideoDto(
    unifiedEntity: any,
    _userId: string | null,
    request?: any,
  ): Promise<any> {
    const user = await this.buildPublicUserDto(unifiedEntity.owner.id);

    // Fetch actual video data from database to get videoPaths and other properties
    let videoPaths: string[] = [];
    let width = 0;
    let height = 0;
    let resolution = 0;
    let systemVersion = null;
    let generationSeconds = 0;
    let settings = {};
    let storageBucket = null;
    let storagePath = null;
    let hidePrompt = false;
    let isHot = false;
    let isNsfw = false;
    let regenerations = 0;
    let reports = 0;
    let originalImageCompletionId = null;
    let inputImageUrl = null;

    try {
      const result = await this.videoRepository
        .createQueryBuilder('v')
        .select([
          'v.video_paths',
          'v.width',
          'v.height',
          'v.resolution',
          'v.system_version',
          'v.generation_seconds',
          'v.settings',
          'v.storage_bucket',
          'v.storage_path',
          'v.hide_prompt',
          'v.is_hot',
          'v.is_nsfw',
          'v.regenerations',
          'v.reports',
          'v.original_image_completion_id',
          'v.input_image_url',
        ])
        .where('v.id = :id', { id: unifiedEntity.entityId })
        .andWhere('v.deletedAt IS NULL')
        .getRawOne();

      if (result) {
        videoPaths = result.video_paths || [];
        width = result.v_width || 0;
        height = result.v_height || 0;
        resolution = result.v_resolution || 0;
        systemVersion = result.system_version;
        generationSeconds = result.generation_seconds || 0;
        settings = result.v_settings || {};
        storageBucket = result.storage_bucket;
        storagePath = result.storage_path;
        hidePrompt = result.v_hide_prompt || false;
        isHot = result.v_is_hot || false;
        isNsfw = result.v_is_nsfw || false;
        regenerations = result.v_regenerations || 0;
        reports = result.v_reports || 0;
        originalImageCompletionId = result.original_image_completion_id;
        inputImageUrl = result.input_image_url;
      }

      this.logger.log('Fetched video data', {
        entityId: unifiedEntity.entityId,
        videoPathsCount: videoPaths.length,
        width,
        height,
        resultFound: !!result,
      });
    } catch (error) {
      this.logger.warn('Failed to fetch video data', {
        entityId: unifiedEntity.entityId,
        error: error.message,
      });
    }

    return {
      id: unifiedEntity.entityId,
      user: user,
      userId: unifiedEntity.owner.id,
      prompt: hidePrompt ? undefined : unifiedEntity.title,
      promptSystem: hidePrompt ? undefined : unifiedEntity.description,
      imageUrl: unifiedEntity.thumbnail,
      width: width,
      height: height,
      resolution: resolution,
      status: 'ready', // Assuming ready since it's in the feed
      progress: 100,
      systemVersion: systemVersion,
      generationSeconds: generationSeconds,
      settings: settings,
      hidePrompt: hidePrompt,

      // Video-to-image generation properties
      originalImageCompletionId: originalImageCompletionId,
      inputImageUrl: inputImageUrl,

      // Video versions and URLs - critical for display
      videoVersions: this.generateVideoVersionsFromVideoPaths(videoPaths),

      storageBucket: storageBucket,
      storagePath: storagePath,

      // Social engagement data
      likes: unifiedEntity.engagement.likes,
      comments: unifiedEntity.engagement.comments,
      regenerations: regenerations,
      reports: reports,

      // User interaction flags
      liked: false, // Would need to check user's likes if userId provided
      isBookmarked: false, // Would need to check user's bookmarks if userId provided

      // Content flags
      privacy: unifiedEntity.privacy || 'public',
      isNsfw: isNsfw,
      isHot: isHot,

      // User info
      username: unifiedEntity.owner.username,
      isUserVerified: false, // Would need to be fetched from user data

      // Timestamps
      createdAt: unifiedEntity.createdAt,
      updatedAt: unifiedEntity.updatedAt,

      // Model information - fetch from original image completion with model filtering
      modelId: await this.getVideoModelId(
        originalImageCompletionId,
        request?.modelIds,
      ),

      // Additional metadata
      tags: unifiedEntity.tags || [],
      metadata: unifiedEntity.metadata || {},
    };
  }

  /**
   * Generate image versions from image paths (matches ImageCompletionResponseMapper.generateImageVersions)
   */
  private generateImageVersionsFromImagePaths(imagePaths: string[]): any {
    if (!imagePaths || imagePaths.length === 0) {
      return null;
    }

    const cdnHost = this.configService.get<string>('CDN_HOST') || '';
    const originalImage = imagePaths[0];
    const originalImageUrl = cdnHost + '/' + originalImage;

    return {
      original: originalImageUrl,
      '96x96': this.composeUrlWithResolution(originalImageUrl, '96x96'),
      '240x240': this.composeUrlWithResolution(originalImageUrl, '240x240'),
      '640x640': this.composeUrlWithResolution(originalImageUrl, '640x640'),
      '1920x1920': this.composeUrlWithResolution(originalImageUrl, '1920x1920'),
    };
  }

  /**
   * Generate image versions with upscales (matches ImageCompletionResponseMapper behavior)
   */
  private async generateImageVersionsWithUpscales(
    imagePaths: string[],
    fullImageEntity: any,
  ): Promise<any> {
    const imageVersions = this.generateImageVersionsFromImagePaths(imagePaths);

    if (!imageVersions || !fullImageEntity?.upscales?.length) {
      return imageVersions;
    }

    // Add upscales if available (matching legacy behavior exactly - lines 193-209)
    imageVersions.upscale = [];

    for (const upscale of fullImageEntity.upscales) {
      if (upscale.status != 'ready') {
        continue;
      }

      try {
        // Use the exact same mapper as legacy code
        const upscaleDto = await this.upscaleResponseMapper.mapImage(upscale);
        imageVersions.upscale.push(upscaleDto);
      } catch (error) {
        this.logger.log('Error mapping upscale', {
          upscaleId: upscale.id,
          error: error.message,
        });
      }
    }

    // Sort by creation date (newest first) - exact same logic as legacy
    imageVersions.upscale.sort((a: any, b: any) => b.createdAt - a.createdAt);

    return imageVersions;
  }

  /**
   * Generate video versions from video paths (matches VideoResponseMapper.generateVideoVersions)
   */
  private generateVideoVersionsFromVideoPaths(videoPaths: string[]): any {
    if (!videoPaths || videoPaths.length === 0) {
      return null;
    }

    // Use the first video path, matching VideoResponseMapper behavior
    return this.generateVideoVersions(videoPaths[0]);
  }

  /**
   * Generate video versions from a single video path (matches VideoResponseMapper.generateVideoVersions)
   */
  private generateVideoVersions(originalVideo: string): any {
    const cdnHost = this.configService.get<string>('CDN_HOST') || '';
    const originalVideoUrl = cdnHost + '/videos/' + originalVideo;

    return {
      original: originalVideoUrl,
      '240p': this.composeUrlWithResolution(originalVideoUrl, '240p'),
    };
  }

  /**
   * Compose URL with resolution (helper for image and video versions)
   */
  private composeUrlWithResolution(
    originalUrl: string,
    resolution: string,
  ): string {
    const [base, extension] = originalUrl.split(/\.(?=[^\.]+$)/);
    return `${base}_${resolution}.${extension}`;
  }

  /**
   * Build cache key for feed requests
   */
  private buildCacheKey(
    feedType: string,
    userId: string | null,
    request: any,
  ): string {
    // Check if modelIds has any non-empty values
    const hasValidModelIds =
      request.modelIds &&
      request.modelIds.length > 0 &&
      request.modelIds.some(
        (id: any) => id && typeof id === 'string' && id.trim().length > 0,
      );

    const keyParts = [
      'feed_v3', // Changed to v3 to invalidate old cache entries and force model filtering
      feedType,
      userId || 'anonymous',
      request.entityType || 'all',
      request.sortBy || 'score',
      request.limit || 20,
      request.cursor || '',
      // Include modelIds in cache key to ensure different filters get different cache entries
      // Treat arrays with only empty strings the same as no modelIds
      hasValidModelIds
        ? `models:${request.modelIds
            .filter((id: any) => id && id.trim())
            .sort()
            .join(',')}`
        : 'models:none',
    ];

    return keyParts.join(':');
  }

  /**
   * Build original image completions array (matches legacy behavior)
   */
  private async buildOriginalImageCompletions(
    fullImageEntity: any,
    userId: string | null,
  ): Promise<any[]> {
    if (!fullImageEntity?.originalImageCompletionRelations?.length) {
      return [];
    }

    const originalImageCompletions = [];

    for (const relation of fullImageEntity.originalImageCompletionRelations) {
      if (relation.originalImageCompletion) {
        try {
          // Build a simplified version of the original image completion
          // (avoiding infinite recursion by not mapping nested originals)
          const originalDto = await this.buildSimplifiedImageCompletionDto(
            relation.originalImageCompletion,
            userId,
          );
          originalImageCompletions.push(originalDto);
        } catch (error) {
          this.logger.log('Error mapping original image completion', {
            originalId: relation.originalImageCompletionId,
            error: error.message,
          });
        }
      }
    }

    return originalImageCompletions;
  }

  /**
   * Build original image completion IDs array
   */
  private buildOriginalImageCompletionIds(fullImageEntity: any): string[] {
    if (!fullImageEntity?.originalImageCompletionRelations?.length) {
      return [];
    }

    return fullImageEntity.originalImageCompletionRelations.map(
      (relation: any) => relation.originalImageCompletionId,
    );
  }

  /**
   * Get model ID for an image completion
   * Applies model filtering if modelIds are provided
   */
  private async getImageModelId(
    fullImageEntity: any,
    modelIds?: string[],
  ): Promise<string | null> {
    if (!fullImageEntity) {
      return null;
    }

    // If no model filtering is applied, return the first model ID
    if (!modelIds || modelIds.length === 0) {
      return fullImageEntity?.models?.length > 0
        ? fullImageEntity.models[0].modelId
        : null;
    }

    // Model filtering is applied - find the first model that matches the filter
    const validModelIds = this.validateModelIds(modelIds);
    if (validModelIds.length === 0) {
      return null;
    }

    // Look for a model that matches the requested model IDs
    const matchingModel = fullImageEntity?.models?.find((model) =>
      validModelIds.includes(model.modelId),
    );

    const result = matchingModel?.modelId || null;

    console.log('🔍 IMAGE MODEL ID LOOKUP RESULT', {
      imageCompletionId: fullImageEntity?.id,
      requestedModelIds: modelIds,
      validModelIds,
      availableModelIds: fullImageEntity?.models?.map((m) => m.modelId) || [],
      matchingModelId: result,
      resultFound: !!result,
    });

    return result;
  }

  /**
   * Get model ID for a video from its original image completion
   * Applies model filtering if modelIds are provided
   */
  private async getVideoModelId(
    originalImageCompletionId: string | null,
    modelIds?: string[],
  ): Promise<string | null> {
    if (!originalImageCompletionId) {
      this.logger.debug(
        'No originalImageCompletionId provided for video model ID lookup',
      );
      return null;
    }

    try {
      const queryBuilder = this.imageCompletionRepository
        .createQueryBuilder('ic')
        .leftJoin('ic.models', 'icm')
        .select('icm.modelId', 'modelId')
        .where('ic.id = :id', { id: originalImageCompletionId })
        .andWhere('ic.deletedAt IS NULL')
        .andWhere('icm.modelId IS NOT NULL');

      // Apply model filtering if provided
      if (modelIds && modelIds.length > 0) {
        const validModelIds = this.validateModelIds(modelIds);
        if (validModelIds.length > 0) {
          queryBuilder.andWhere('icm.modelId IN (:...validModelIds)', {
            validModelIds,
          });

          console.log('🔍 APPLIED MODEL FILTERING TO VIDEO MODEL LOOKUP', {
            originalImageCompletionId,
            requestedModelIds: modelIds,
            validModelIds,
          });
        } else {
          // No valid model IDs - return null to indicate no match
          return null;
        }
      }

      const result = await queryBuilder.limit(1).getRawOne();

      console.log('🔍 VIDEO MODEL ID LOOKUP RESULT', {
        originalImageCompletionId,
        requestedModelIds: modelIds,
        modelId: result?.modelId || null,
        resultFound: !!result,
      });

      return result?.modelId || null;
    } catch (error) {
      this.logger.debug('Error fetching video model ID', {
        originalImageCompletionId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Build models array from image completion models relation
   */
  private async buildModelsArray(fullImageEntity: any): Promise<any[]> {
    this.logger.debug('buildModelsArray called', {
      imageCompletionId: fullImageEntity?.id,
      hasModels: !!fullImageEntity?.models,
      modelsLength: fullImageEntity?.models?.length || 0,
    });

    if (!fullImageEntity?.models?.length) {
      this.logger.debug('No models found for image completion', {
        imageCompletionId: fullImageEntity?.id,
      });
      return [];
    }

    const models = [];
    for (const imageCompletionModel of fullImageEntity.models) {
      if (!imageCompletionModel.modelId) {
        continue;
      }

      try {
        const model =
          imageCompletionModel.model ??
          (await this.modelProvider.get(imageCompletionModel.modelId));

        if (model) {
          // Use the same mapping as the original ImageCompletionResponseMapper
          const modelDto = await this.modelResponseMapper.map(
            model,
            true,
            false,
          );
          models.push(modelDto);
        }
      } catch (error) {
        this.logger.debug('Error mapping image models in feed service', {
          imageCompletionId: fullImageEntity.id,
          imageCompletionModelId: imageCompletionModel?.id,
          modelId: imageCompletionModel?.modelId,
          error: error.message,
        });
      }
    }

    this.logger.debug('buildModelsArray completed', {
      imageCompletionId: fullImageEntity?.id,
      modelsBuilt: models.length,
      modelIds: models.map((m) => m.id).slice(0, 3),
    });

    return models;
  }

  /**
   * Build complete image edit data using the proper ImageEditResponseMapper
   */
  private async buildImageEdit(fullImageEntity: any): Promise<any> {
    if (!fullImageEntity?.imageEditImageCompletion?.imageEdit) {
      return null;
    }

    try {
      const imageEdit = fullImageEntity.imageEditImageCompletion.imageEdit;

      // Validate imageEdit entity before mapping
      if (!imageEdit.id) {
        this.logger.warn('ImageEdit entity missing required fields', {
          imageCompletionId: fullImageEntity.id,
          imageEdit,
        });
        return null;
      }

      // Use the proper ImageEditResponseMapper to ensure all properties are included
      return await this.imageEditResponseMapper.map(
        imageEdit,
        false, // isInternal: false for public feed responses
        true, // allowNesting: true to include nested objects like originalImageCompletions
        true, // includeImageEdits: true for complete edit data
      );
    } catch (error) {
      this.logger.error('Failed to map image edit in feed', {
        imageCompletionId: fullImageEntity.id,
        imageEditId: fullImageEntity.imageEditImageCompletion?.imageEdit?.id,
        error: error.message,
        stack: error.stack,
      });

      // Return null to gracefully handle mapping failures
      return null;
    }
  }

  /**
   * Check if user has liked the image
   */
  private async checkUserLiked(
    imageCompletionId: string,
    userId: string,
  ): Promise<boolean> {
    try {
      const likeCount = await this.dataSource.query(
        'SELECT COUNT(*) as count FROM image_completion_like WHERE image_completion_id = $1 AND user_id = $2 AND deleted_at IS NULL',
        [imageCompletionId, userId],
      );
      return parseInt(likeCount[0]?.count || '0') > 0;
    } catch (error) {
      this.logger.log('Error checking user like', {
        imageCompletionId,
        userId,
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Check if user has bookmarked the image
   */
  private async checkUserBookmarked(
    imageCompletionId: string,
    userId: string,
  ): Promise<boolean> {
    try {
      const bookmarkCount = await this.dataSource.query(
        'SELECT COUNT(*) as count FROM bookmark WHERE entity_id = $1 AND entity_type = $2 AND user_id = $3 AND deleted_at IS NULL',
        [imageCompletionId, 'image', userId],
      );
      return parseInt(bookmarkCount[0]?.count || '0') > 0;
    } catch (error) {
      this.logger.log('Error checking user bookmark', {
        imageCompletionId,
        userId,
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Build simplified image completion DTO (for original image completions to avoid recursion)
   */
  private async buildSimplifiedImageCompletionDto(
    imageEntity: any,
    userId: string | null,
  ): Promise<any> {
    const user = await this.buildPublicUserDto(imageEntity.userId);

    return {
      id: imageEntity.id,
      user: user,
      prompt: imageEntity.hidePrompt ? null : imageEntity.prompt,
      status: imageEntity.status,
      privacy: imageEntity.privacy,
      likes: imageEntity.likes || 0,
      comments: imageEntity.comments || 0,
      regenerations: imageEntity.regenerations || 0,
      isHot: imageEntity.isHot || false,
      isNsfw: imageEntity.isNsfw || false,
      createdAt: imageEntity.createdAt,
      imageVersions: this.generateImageVersionsFromImagePaths(
        imageEntity.imagePaths || [],
      ),
      // Simplified - no nested original completions to avoid recursion
      originalImageCompletions: [],
      originalImageCompletionIds: [],
      imageEdit: null,
      liked: userId ? await this.checkUserLiked(imageEntity.id, userId) : false,
      isBookmarked: userId
        ? await this.checkUserBookmarked(imageEntity.id, userId)
        : false,
    };
  }

  /**
   * Add metadata to feed response
   */
  private addMetadata(
    response: FeedResponseDto,
    feedType: string,
    startTime: number,
    cacheStatus: 'hit' | 'miss' | 'partial',
    request?: any,
  ): FeedResponseDto {
    response.metadata = {
      ...response.metadata,
      feedType,
      generatedAt: new Date().toISOString(),
      cacheStatus,
      generationTimeMs: Date.now() - startTime,
      // Add request signature to help identify caching issues
      requestSignature: request
        ? this.generateRequestSignature(request)
        : undefined,
    };

    return response;
  }

  private generateRequestSignature(request: any): string {
    const signature = {
      cursor: request.cursor,
      cursorTimestamp: request.cursorTimestamp,
      limit: request.limit,
      sortBy: request.sortBy,
      entityType: request.entityType,
      timestamp: Date.now(),
    };
    return Buffer.from(JSON.stringify(signature))
      .toString('base64')
      .substring(0, 16);
  }

  /**
   * Get user feed - content created by a specific user (supports both userId and username)
   * Uses FeedEntryEntity for pre-computed, personalized feed entries
   */
  async getUserFeed(
    userIdentifier: string,
    currentUserId?: string,
    request?: any,
  ): Promise<FeedResponseDto> {
    console.log('🔍 STEP 2: getUserFeed called', {
      userIdentifier,
      currentUserId,
      modelIds: request?.modelIds,
      hasModelIds: !!request?.modelIds?.length,
    });
    this.logger.error('🔍 STEP 2: getUserFeed called', {
      userIdentifier,
      currentUserId,
      modelIds: request?.modelIds,
      hasModelIds: !!request?.modelIds?.length,
    });
    const startTime = Date.now();

    try {
      this.logger.log('Generating user feed', {
        userIdentifier,
        currentUserId,
        request,
      });

      // Resolve user ID from identifier (UUID or username)
      const userId = await this.resolveUserId(userIdentifier);

      const feedRequest = request || {
        limit: 20,
        sortBy: 'score',
        sortOrder: 'DESC',
      };

      // Skip cache for requests with model IDs to ensure filtering is applied
      const hasModelIds =
        feedRequest.modelIds && feedRequest.modelIds.length > 0;
      let cachedFeed = null;
      let cacheKey = '';

      if (!hasModelIds) {
        // Check cache only for requests without model IDs
        cacheKey = this.buildCacheKey('user', userId, feedRequest);
        cachedFeed = await this.feedCacheService.get(cacheKey, userId);
      }

      console.log('🔍 CACHE DEBUG', {
        cacheKey,
        hasModelIds,
        modelIds: feedRequest.modelIds,
        skipCache: hasModelIds,
        cachedFeed: !!cachedFeed,
      });
      this.logger.error('🔍 CACHE DEBUG', {
        cacheKey,
        hasModelIds,
        modelIds: feedRequest.modelIds,
        skipCache: hasModelIds,
        cachedFeed: !!cachedFeed,
      });

      if (cachedFeed) {
        console.log('🔍 CACHE HIT - RETURNING CACHED RESULT', {
          userId,
          cacheKey,
        });
        this.logger.error('🔍 CACHE HIT - RETURNING CACHED RESULT', {
          userId,
          cacheKey,
        });
        return this.addMetadata(cachedFeed, 'user', startTime, 'hit');
      } else {
        console.log('🔍 CACHE MISS OR SKIPPED - PROCEEDING TO GENERATE FEED', {
          userId,
          hasModelIds,
        });
        this.logger.error(
          '🔍 CACHE MISS OR SKIPPED - PROCEEDING TO GENERATE FEED',
          { userId, hasModelIds },
        );
      }

      // Get pre-computed feed entries for the user from FeedEntryEntity
      let feedEntries = [];
      let feedEntriesError = null;

      try {
        console.log('🔍 ENTERING TRY BLOCK FOR FEED ENTRIES', {
          userId,
          currentUserId,
        });
        this.logger.error('🔍 ENTERING TRY BLOCK FOR FEED ENTRIES', {
          userId,
          currentUserId,
        });

        // Check if we should use fallback for privacy=all requests by owners
        const isOwner = currentUserId && currentUserId === userId;
        const shouldUseFallback = isOwner && feedRequest.privacy === 'all';

        console.log('🔍 FALLBACK DECISION', {
          isOwner,
          shouldUseFallback,
          currentUserId,
          userId,
          privacy: feedRequest.privacy,
        });
        this.logger.error('🔍 FALLBACK DECISION', {
          isOwner,
          shouldUseFallback,
          currentUserId,
          userId,
          privacy: feedRequest.privacy,
        });

        if (shouldUseFallback) {
          console.log('🔍 USING FALLBACK PATH', {
            userId,
            currentUserId,
            privacy: feedRequest.privacy,
          });
          this.logger.error('🔍 USING FALLBACK PATH', {
            userId,
            currentUserId,
            privacy: feedRequest.privacy,
          });
          feedEntries = []; // Force fallback to getUserContentEntries
        } else {
          console.log('🔍 USING NORMAL PATH - WILL CALL getUserFeedEntries', {
            userId,
            currentUserId,
            privacy: feedRequest.privacy,
          });
          this.logger.error(
            '🔍 USING NORMAL PATH - WILL CALL getUserFeedEntries',
            { userId, currentUserId, privacy: feedRequest.privacy },
          );
          this.logger.log('Attempting to get pre-computed feed entries', {
            userId,
            currentUserId,
            feedRequest: {
              modelIds: feedRequest.modelIds,
              modelIdsLength: feedRequest.modelIds?.length,
              entityType: feedRequest.entityType,
              privacy: feedRequest.privacy,
            },
          });

          console.log('🔍 ABOUT TO CALL getUserFeedEntries', {
            userId,
            feedRequest,
            currentUserId,
          });
          this.logger.error('🔍 ABOUT TO CALL getUserFeedEntries', {
            userId,
            feedRequest,
            currentUserId,
          });

          feedEntries = await this.getUserFeedEntries(
            userId,
            feedRequest,
            currentUserId,
          );

          console.log('🔍 RETURNED FROM getUserFeedEntries', {
            userId,
            feedEntriesCount: feedEntries.length,
          });
          this.logger.error('🔍 RETURNED FROM getUserFeedEntries', {
            userId,
            feedEntriesCount: feedEntries.length,
          });

          console.log('🔍 STEP 2: Pre-computed feed entries result', {
            userId,
            feedEntriesCount: feedEntries.length,
            hasModelIds: !!feedRequest.modelIds?.length,
            modelIds: feedRequest.modelIds,
          });
          this.logger.error('🔍 STEP 2: Pre-computed feed entries result', {
            userId,
            feedEntriesCount: feedEntries.length,
            hasModelIds: !!feedRequest.modelIds?.length,
            modelIds: feedRequest.modelIds,
          });

          // Add explicit logging to track which path is taken
          if (feedEntries.length === 0) {
            console.log(
              '🔍 STEP 2: No pre-computed feed entries found - will use fallback',
              {
                userId,
                hasModelIds: !!feedRequest.modelIds?.length,
                modelIds: feedRequest.modelIds,
              },
            );
            this.logger.error(
              '🔍 STEP 2: No pre-computed feed entries found - will use fallback',
              {
                userId,
                hasModelIds: !!feedRequest.modelIds?.length,
                modelIds: feedRequest.modelIds,
              },
            );
          } else {
            console.log('🔍 STEP 2: Using pre-computed feed entries', {
              userId,
              feedEntriesCount: feedEntries.length,
              hasModelIds: !!feedRequest.modelIds?.length,
              modelIds: feedRequest.modelIds,
            });
            this.logger.error('🔍 STEP 2: Using pre-computed feed entries', {
              userId,
              feedEntriesCount: feedEntries.length,
              hasModelIds: !!feedRequest.modelIds?.length,
              modelIds: feedRequest.modelIds,
            });
          }
        }
      } catch (error) {
        feedEntriesError = error;
        this.logger.warn(
          'Failed to retrieve pre-computed feed entries, falling back to direct queries',
          {
            userId,
            error: error.message,
            errorType: error.constructor.name,
          },
        );
      }

      this.logger.log('Feed entries retrieval result', {
        userId,
        feedEntriesCount: feedEntries.length,
        willUseFallback: feedEntries.length === 0,
        hadError: !!feedEntriesError,
      });

      // If no pre-computed entries exist, fall back to direct content queries
      let userContentEntries;
      let hasMoreItems = false;

      if (feedEntries.length === 0) {
        console.log('🔍 STEP 2: FALLBACK PATH - Using getUserContentEntries', {
          userId,
          hasModelIds: !!feedRequest.modelIds?.length,
          modelIds: feedRequest.modelIds,
          modelIdsCount: feedRequest.modelIds?.length || 0,
        });
        this.logger.error(
          '🔍 STEP 2: FALLBACK PATH - Using getUserContentEntries',
          {
            userId,
            hasModelIds: !!feedRequest.modelIds?.length,
            modelIds: feedRequest.modelIds,
            modelIdsCount: feedRequest.modelIds?.length || 0,
          },
        );

        // Request one extra item to determine if there are more
        const extendedRequest = {
          ...feedRequest,
          limit: (feedRequest.limit || 20) + 1,
        };
        const allEntries = await this.getUserContentEntries(
          userId,
          extendedRequest,
          currentUserId,
        );

        // Check if we have more items than requested
        hasMoreItems = allEntries.length > (feedRequest.limit || 20);

        // Take only the requested number of items
        userContentEntries = allEntries.slice(0, feedRequest.limit || 20);
      } else {
        // Convert feed entries to unified format
        userContentEntries = feedEntries.map((entry) => ({
          entityType: entry.entityType,
          entityId: entry.entityId,
          score: entry.score,
          createdAt: entry.createdAt,
        }));

        // For pre-computed entries, check if we have more by looking at the query result
        hasMoreItems = feedEntries.length === (feedRequest.limit || 20);

        // DEBUGGING: Log the feed entries we're about to process
        this.logger.log('Using pre-computed feed entries', {
          userId,
          entriesCount: userContentEntries.length,
          sampleEntries: userContentEntries.slice(0, 3).map((entry) => ({
            entityType: entry.entityType,
            entityId: entry.entityId,
          })),
        });
      }

      // DEBUGGING: Temporarily disabled validation to isolate the issue
      // TODO: Re-enable once the core entity fetching issue is resolved
      if (userContentEntries.length > 0) {
        const validationResults = await this.validateFeedEntries(
          userContentEntries,
        );
        const existingEntries = validationResults.filter(
          (result) => result.exists,
        );
        const missingEntries = validationResults.filter(
          (result) => !result.exists,
        );

        this.logger.log('Feed entries validation results', {
          userId,
          totalEntries: validationResults.length,
          existingEntries: existingEntries.length,
          missingEntries: missingEntries.length,
          sampleMissing: missingEntries.slice(0, 3),
        });

        if (missingEntries.length > 0) {
          this.logger.warn('Found orphaned feed entries', {
            userId,
            orphanedCount: missingEntries.length,
            orphanedEntries: missingEntries.slice(0, 5),
          });
        }

        // Filter out missing entries to prevent "Entity not found" errors
        userContentEntries = userContentEntries.filter((entry) => {
          const validation = validationResults.find(
            (v) =>
              v.entityType === entry.entityType &&
              v.entityId === entry.entityId,
          );
          return validation?.exists === true;
        });
      }

      this.logger.log('Preparing to build feed response', {
        userId,
        userContentEntriesCount: userContentEntries.length,
        sampleEntries: userContentEntries.slice(0, 3).map((entry) => ({
          entityType: entry.entityType,
          entityId: entry.entityId,
        })),
        hasMoreItems,
      });

      // Convert to feed response with ownership validation
      console.log('🔥 ABOUT TO CALL buildFeedResponse', {
        userContentEntriesCount: userContentEntries.length,
        hasModelIds: !!feedRequest.modelIds?.length,
        modelIds: feedRequest.modelIds,
      });

      const feedResponse = await this.buildFeedResponse(
        userContentEntries,
        feedRequest,
        currentUserId,
        hasMoreItems,
        userId, // Pass the target user ID for ownership validation
      );

      // Cache the result
      await this.feedCacheService.set(cacheKey, feedResponse, 300); // 5 minutes TTL

      return this.addMetadata(feedResponse, 'user', startTime, 'miss');
    } catch (error) {
      this.logger.error('Error getting user feed', {
        userIdentifier,
        currentUserId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get pre-computed feed entries for a specific user from FeedEntryEntity
   * This is the correct architectural approach for user feeds
   */
  private async getUserFeedEntries(
    userId: string,
    request: any,
    currentUserId?: string,
  ): Promise<FeedEntryEntity[]> {
    console.log('🔍 getUserFeedEntries DEBUG', {
      userId,
      currentUserId,
      requestKeys: Object.keys(request || {}),
      modelIds: request?.modelIds,
      modelIdsLength: request?.modelIds?.length,
      entityType: request?.entityType,
      fullRequest: request,
    });
    this.logger.error('🔍 getUserFeedEntries DEBUG', {
      userId,
      currentUserId,
      requestKeys: Object.keys(request || {}),
      modelIds: request?.modelIds,
      modelIdsLength: request?.modelIds?.length,
      entityType: request?.entityType,
      fullRequest: request,
    });

    try {
      const queryBuilder = this.feedEntryRepository
        .createQueryBuilder('fe')
        .where('fe.userId = :userId', { userId });

      // Apply entity type filtering
      if (request.entityType && request.entityType !== EntityType.ALL) {
        queryBuilder.andWhere('fe.entityType = :entityType', {
          entityType: request.entityType,
        });
      }

      // Apply privacy filtering by joining with content tables
      const privacyFilter = request.privacy || 'public';
      const isOwner = currentUserId && currentUserId === userId;

      this.logger.log('Privacy filter applied', {
        isOwner,
        privacyFilter,
        userId,
        currentUserId,
      });

      // Determine if we need to add joins for privacy or model filtering
      const needsModelFiltering =
        request.modelIds && request.modelIds.length > 0;
      const needsPrivacyFiltering = !(isOwner && privacyFilter === 'all');

      console.log('🔍 JOIN CONDITIONS DEBUG', {
        needsModelFiltering,
        needsPrivacyFiltering,
        requestModelIds: request.modelIds,
        modelIdsLength: request.modelIds?.length,
        isOwner,
        privacyFilter,
      });
      this.logger.error('🔍 JOIN CONDITIONS DEBUG', {
        needsModelFiltering,
        needsPrivacyFiltering,
        requestModelIds: request.modelIds,
        modelIdsLength: request.modelIds?.length,
        isOwner,
        privacyFilter,
      });

      // Add JOINs if needed for privacy or model filtering
      if (needsPrivacyFiltering || needsModelFiltering) {
        // Add JOINs to get privacy and model information
        queryBuilder
          .leftJoin(
            'image_completion',
            'ic',
            'fe.entityType = :imageType AND fe.entityId = ic.id',
            { imageType: 'image' },
          )
          .leftJoin(
            'video',
            'v',
            'fe.entityType = :videoType AND fe.entityId = v.id',
            { videoType: 'video' },
          );

        // Model filtering will be handled via EXISTS subqueries in the WHERE clause
        // No additional joins needed for model filtering
      }

      // Apply privacy filtering based on ownership and requested privacy level
      if (isOwner && privacyFilter === 'all') {
        // Owner requesting all content - no privacy filter needed
        this.logger.log(
          'Owner requesting all content - no privacy filter applied',
        );
      } else {
        // Need to filter by privacy level
        const targetPrivacy = isOwner ? privacyFilter : 'public';

        this.logger.log('Applying privacy filter', {
          targetPrivacy,
          isOwner,
        });

        // Apply privacy filtering using JOINs
        queryBuilder.andWhere(
          `(
            (fe.entityType = 'image' AND ic.privacy = :targetPrivacy) OR
            (fe.entityType = 'video' AND v.privacy = :targetPrivacy)
          )`,
          { targetPrivacy },
        );
      }

      // Apply sorting
      if (request.sortBy === 'createdAt') {
        queryBuilder.orderBy('fe.createdAt', request.sortOrder || 'DESC');
      } else {
        // Default to score-based sorting for pre-computed feeds
        queryBuilder.orderBy('fe.score', 'DESC');
        queryBuilder.addOrderBy('fe.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.limit(request.limit || 20);

      // Apply cursor-based pagination if provided
      if (request.cursor) {
        if (request.sortBy === 'createdAt') {
          // For date-based sorting, use cursor as timestamp
          const cursorDate = new Date(request.cursor);
          if (!isNaN(cursorDate.getTime())) {
            queryBuilder.andWhere('fe.createdAt < :cursorDate', {
              cursorDate: cursorDate,
            });
          }
        } else {
          // For score-based sorting, use cursor as score
          queryBuilder.andWhere(
            '(fe.score < :cursorScore OR (fe.score = :cursorScore AND fe.createdAt < :cursorCreatedAt))',
            {
              cursorScore: request.cursor,
              cursorCreatedAt: request.cursorTimestamp || new Date(),
            },
          );
        }
      }

      // Apply model IDs filtering if provided
      if (request.modelIds && request.modelIds.length > 0) {
        this.logger.log('Model IDs filtering requested', {
          userId,
          originalModelIds: request.modelIds,
          modelIdsCount: request.modelIds.length,
        });

        try {
          const validModelIds = this.validateModelIds(request.modelIds);

          this.logger.log('Model IDs validation completed', {
            userId,
            originalModelIds: request.modelIds,
            validModelIds,
            originalCount: request.modelIds.length,
            validCount: validModelIds.length,
          });

          if (validModelIds.length === 0) {
            // All model IDs were invalid - apply impossible filter to return 0 results
            queryBuilder.andWhere('1 = 0'); // This will always be false, returning 0 results

            this.logger.warn(
              'All model IDs invalid - applying impossible filter to return 0 results',
              {
                userId,
                originalModelIds: request.modelIds,
              },
            );
          } else {
            // Apply the model filtering condition using EXISTS subqueries for precise filtering
            // Use correct database column names (snake_case) for raw SQL
            queryBuilder.andWhere(
              `(
              (fe.entity_type = 'image' AND EXISTS (
                SELECT 1 FROM image_completion_model icm_sub
                WHERE icm_sub.image_completion_id = ic.id
                AND icm_sub.model_id IN (:...validModelIds)
              )) OR
              (fe.entity_type = 'video' AND EXISTS (
                SELECT 1 FROM image_completion_model icm_video_sub
                WHERE icm_video_sub.image_completion_id = v.original_image_completion_id
                AND icm_video_sub.model_id IN (:...validModelIds)
              ))
            )`,
              { validModelIds },
            );

            // Note: DISTINCT removed to debug if it was causing issues

            // Debug: Log the actual SQL query being generated
            const sqlQuery = queryBuilder.getSql();
            const parameters = queryBuilder.getParameters();

            console.log(
              '🔥 MULTIPLE MODEL IDS - getUserFeedEntries SQL DEBUG',
              {
                validModelIds,
                sqlQuery: sqlQuery.substring(0, 500) + '...',
                parameters,
                whereClause: sqlQuery.includes('IS NOT NULL')
                  ? 'Contains NULL checks'
                  : 'No NULL checks',
              },
            );
            this.logger.error(
              '🔥 MULTIPLE MODEL IDS - getUserFeedEntries SQL DEBUG',
              {
                validModelIds,
                sqlQuery: sqlQuery.substring(0, 500) + '...',
                parameters,
              },
            );

            this.logger.log(
              'Database-level model filtering applied to feed entries',
              {
                userId,
                originalModelIds: request.modelIds,
                validModelIds,
                modelIdsCount: validModelIds.length,
              },
            );
          }
        } catch (error) {
          this.logger.error('Error applying model IDs filter', {
            userId,
            modelIds: request.modelIds,
            error: error.message,
            stack: error.stack,
          });
          // Don't throw the error, just log it and continue without model filtering
          // This ensures the API doesn't break for invalid model IDs
        }
      }

      // Filter out expired entries
      queryBuilder.andWhere('(fe.expiresAt IS NULL OR fe.expiresAt > NOW())');

      let feedEntries: FeedEntryEntity[] = [];
      try {
        feedEntries = await queryBuilder.getMany();
      } catch (error) {
        this.logger.error('Error executing user feed query', {
          userId,
          error: error.message,
          modelIds: request.modelIds,
          stack: error.stack,
        });

        // If the query fails (possibly due to model ID filtering), try without model filtering
        if (request.modelIds && request.modelIds.length > 0) {
          this.logger.warn(
            'Retrying query without model ID filtering due to error',
            { userId },
          );

          // Rebuild query without model filtering
          const fallbackQueryBuilder =
            this.feedEntryRepository.createQueryBuilder('fe');
          fallbackQueryBuilder.where('fe.userId = :userId', { userId });

          // Apply other filters (privacy, entity type, etc.) but skip model filtering
          if (
            request.privacy &&
            request.privacy !== UserFeedPrivacyFilter.ALL
          ) {
            fallbackQueryBuilder.andWhere('fe.privacy = :privacy', {
              privacy: request.privacy,
            });
          }

          if (
            request.entityType &&
            request.entityType !== UserFeedEntityType.ALL
          ) {
            fallbackQueryBuilder.andWhere('fe.entityType = :entityType', {
              entityType: request.entityType,
            });
          }

          fallbackQueryBuilder.andWhere(
            '(fe.expiresAt IS NULL OR fe.expiresAt > NOW())',
          );
          fallbackQueryBuilder.orderBy(
            `fe.${request.sortBy}`,
            request.sortOrder,
          );
          fallbackQueryBuilder.limit(request.limit);

          if (request.cursor) {
            fallbackQueryBuilder.andWhere(`fe.${request.sortBy} < :cursor`, {
              cursor: request.cursor,
            });
          }

          try {
            feedEntries = await fallbackQueryBuilder.getMany();
            this.logger.log('Fallback query succeeded', {
              userId,
              entriesFound: feedEntries.length,
            });
          } catch (fallbackError) {
            this.logger.error('Fallback query also failed', {
              userId,
              error: fallbackError.message,
            });
            // Return empty array as last resort
            feedEntries = [];
          }
        } else {
          // If it's not a model filtering issue, return empty array
          feedEntries = [];
        }
      }

      this.logger.log('Retrieved user feed entries from FeedEntryEntity', {
        userId,
        entriesFound: feedEntries.length,
        sortBy: request.sortBy,
        entityType: request.entityType,
        modelIds: request.modelIds,
        modelIdsCount: request.modelIds?.length || 0,
      });

      // CRITICAL SECURITY: Validate that all feed entries belong to the correct user
      if (feedEntries.length > 0) {
        const ownershipViolations = feedEntries.filter(
          (entry) => entry.userId !== userId,
        );

        if (ownershipViolations.length > 0) {
          this.logger.error(
            'CRITICAL: Feed entries ownership violations detected',
            {
              userId,
              totalEntries: feedEntries.length,
              violationCount: ownershipViolations.length,
              violations: ownershipViolations.slice(0, 5).map((entry) => ({
                entryId: entry.id,
                entityType: entry.entityType,
                entityId: entry.entityId,
                expectedUserId: userId,
                actualUserId: entry.userId,
              })),
            },
          );

          // Filter out violating entries to prevent data leakage
          feedEntries = feedEntries.filter((entry) => entry.userId === userId);

          this.logger.warn(
            'Filtered out ownership violations from feed entries',
            {
              originalCount: feedEntries.length + ownershipViolations.length,
              filteredCount: feedEntries.length,
              removedCount: ownershipViolations.length,
            },
          );
        }

        const entityIds = feedEntries.map((entry) => entry.entityId);
        this.logger.log('Feed entries validated and retrieved', {
          userId,
          entityIds: entityIds.slice(0, 5), // Log first 5 for debugging
          totalEntries: feedEntries.length,
          ownershipValidated: true,
        });
      }

      return feedEntries;
    } catch (error) {
      this.logger.error('Error retrieving user feed entries', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Validates and filters model IDs to ensure they are valid UUIDs
   * This prevents PostgreSQL UUID validation errors
   */
  private validateModelIds(modelIds: string[]): string[] {
    if (!modelIds || modelIds.length === 0) {
      return [];
    }

    // Check if all model IDs are empty strings - treat as no filter
    const nonEmptyIds = modelIds.filter(
      (id) => id && typeof id === 'string' && id.trim().length > 0,
    );
    if (nonEmptyIds.length === 0) {
      // All IDs are empty strings - treat as no filter by returning undefined
      // This signals to the calling code that no filter should be applied
      this.logger.debug(
        'All model IDs are empty strings - treating as no filter',
        {
          originalModelIds: modelIds,
        },
      );
      return [];
    }

    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const validModelIds = modelIds.filter((id) => {
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        this.logger.debug('Model ID validation failed: empty or invalid type', {
          id,
          type: typeof id,
        });
        return false;
      }
      const isValid = uuidRegex.test(id.trim());
      this.logger.debug('Model ID validation result', {
        id: id.trim(),
        isValid,
        regexPattern: uuidRegex.toString(),
      });
      return isValid;
    });

    if (validModelIds.length !== modelIds.length) {
      this.logger.warn(
        'Some model IDs were filtered out due to invalid UUID format',
        {
          originalCount: modelIds.length,
          validCount: validModelIds.length,
          invalidIds: modelIds.filter((id) => !validModelIds.includes(id)),
        },
      );
    }

    return validModelIds;
  }

  /**
   * Get content entries created by a specific user (images and videos)
   * FALLBACK METHOD: Used when no pre-computed feed entries exist
   */
  private async getUserContentEntries(
    userId: string,
    request: any,
    currentUserId?: string,
  ): Promise<any[]> {
    this.logger.log('getUserContentEntries called', {
      userId,
      currentUserId,
      requestKeys: Object.keys(request || {}),
      requestModelIds: request?.modelIds,
      requestModelIdsLength: request?.modelIds?.length || 0,
      requestEntityType: request?.entityType,
    });

    const limit = request.limit || 20;
    const entityTypeFilter = request.entityType || 'all';
    const sortBy = request.sortBy || 'createdAt';
    const sortOrder = request.sortOrder || 'DESC';
    const privacyFilter = request.privacy || 'public';
    const modelIds = request.modelIds || [];
    const includeNsfw = request.includeNsfw || false;
    const startDate = request.startDate;
    const endDate = request.endDate;
    const cursor = request.cursor;

    this.logger.log('getUserContentEntries parameters extracted', {
      userId,
      limit,
      entityTypeFilter,
      modelIds,
      modelIdsLength: modelIds.length,
    });

    // Determine if current user is the content owner
    const isOwner = currentUserId && currentUserId === userId;

    // Build queries for user content
    const queries = [];

    // Add image query if needed
    if (entityTypeFilter === 'all' || entityTypeFilter === 'image') {
      const imageQuery = this.imageCompletionRepository
        .createQueryBuilder('ic')
        .select([
          '\'image\' as "entityType"',
          'ic.id as "entityId"',
          'ic.user_id as "userId"', // CRITICAL: Include user_id for ownership validation
          'ic.created_at as "createdAt"',
          'ic.likes',
          'ic.comments',
          'ic.regenerations',
          'ic.reports',
          'ic.privacy',
          'ic.is_nsfw as "isNsfw"',
          'ic.status',
        ])
        .where('ic.user_id = :userId', { userId })
        .andWhere('ic.deleted_at IS NULL')
        .andWhere('ic.status = :status', { status: 'ready' }); // Only ready content

      // Apply privacy filtering
      if (isOwner) {
        // Owner can filter by privacy preference
        if (privacyFilter === 'public') {
          imageQuery.andWhere('ic.privacy = :privacy', { privacy: 'public' });
        } else if (privacyFilter === 'private') {
          imageQuery.andWhere('ic.privacy = :privacy', { privacy: 'private' });
        }
        // 'all' means no privacy filter for owners
      } else {
        // Non-owners can only see public content regardless of privacy parameter
        imageQuery.andWhere('ic.privacy = :privacy', { privacy: 'public' });
      }

      // Apply NSFW filtering
      if (!includeNsfw) {
        imageQuery.andWhere('ic.is_nsfw = :isNsfw', { isNsfw: false });
      }

      // Apply model IDs filtering for images
      if (modelIds && modelIds.length > 0) {
        console.log('🚨🚨🚨 MODEL FILTERING IN getUserContentEntries 🚨🚨🚨', {
          modelIds,
        });
        this.logger.error(
          '🚨🚨🚨 MODEL FILTERING IN getUserContentEntries 🚨🚨🚨',
          { modelIds },
        );

        const validModelIds = this.validateModelIds(modelIds);

        if (validModelIds.length > 0) {
          console.log(
            '🔥 MULTIPLE MODEL IDS - getUserContentEntries IMAGE SQL DEBUG',
            { validModelIds },
          );
          imageQuery
            .leftJoin('ic.models', 'ic_models')
            .andWhere('ic_models.modelId IN (:...validModelIds)', {
              validModelIds,
            });

          // Debug the image query SQL
          const imageQuerySql = imageQuery.getSql();
          const imageQueryParams = imageQuery.getParameters();

          console.log('🔥 IMAGE QUERY SQL', {
            validModelIds,
            sqlQuery: imageQuerySql.substring(0, 300) + '...',
            parameters: imageQueryParams,
          });
          this.logger.error('🔥 MULTIPLE MODEL IDS - IMAGE QUERY APPLIED', {
            originalModelIds: modelIds,
            validModelIds,
            sqlQuery: imageQuerySql.substring(0, 300) + '...',
          });
        } else {
          // All model IDs were invalid - apply impossible filter to return 0 results
          imageQuery.andWhere('1 = 0');

          console.log(
            '🚨🚨🚨 ALL MODEL IDS INVALID - APPLYING IMPOSSIBLE FILTER 🚨🚨🚨',
            { modelIds },
          );
          this.logger.error(
            '🚨🚨🚨 ALL MODEL IDS INVALID - APPLYING IMPOSSIBLE FILTER 🚨🚨🚨',
            {
              originalModelIds: modelIds,
            },
          );
        }
      }

      // Apply date range filtering
      if (startDate) {
        imageQuery.andWhere('ic.created_at >= :startDate', { startDate });
      }
      if (endDate) {
        imageQuery.andWhere('ic.created_at <= :endDate', { endDate });
      }

      // Apply cursor-based pagination
      if (cursor) {
        if (sortBy === 'createdAt') {
          // For date-based sorting, use cursor as timestamp
          const cursorDate = new Date(cursor);
          if (!isNaN(cursorDate.getTime())) {
            const operator = sortOrder.toUpperCase() === 'ASC' ? '>' : '<';
            imageQuery.andWhere(`ic.created_at ${operator} :cursorDate`, {
              cursorDate: cursorDate,
            });
          }
        } else {
          // For other sorting, use cursor as timestamp fallback
          const cursorDate = new Date(cursor);
          if (!isNaN(cursorDate.getTime())) {
            imageQuery.andWhere('ic.created_at < :cursorDate', {
              cursorDate: cursorDate,
            });
          }
        }
      }

      queries.push(imageQuery);
    }

    // Add video query if needed
    if (entityTypeFilter === 'all' || entityTypeFilter === 'video') {
      const videoQuery = this.videoRepository
        .createQueryBuilder('v')
        .select([
          '\'video\' as "entityType"',
          'v.id as "entityId"',
          'v.user_id as "userId"', // CRITICAL: Include user_id for ownership validation
          'v.created_at as "createdAt"',
          'v.likes',
          'v.comments',
          'v.regenerations',
          'v.reports',
          'v.privacy',
          'v.is_nsfw as "isNsfw"',
          'v.status',
        ])
        .where('v.user_id = :userId', { userId })
        .andWhere('v.deleted_at IS NULL')
        .andWhere('v.status = :status', { status: 'ready' }); // Only ready content

      // Apply privacy filtering
      if (isOwner) {
        // Owner can filter by privacy preference
        if (privacyFilter === 'public') {
          videoQuery.andWhere('v.privacy = :privacy', { privacy: 'public' });
        } else if (privacyFilter === 'private') {
          videoQuery.andWhere('v.privacy = :privacy', { privacy: 'private' });
        }
        // 'all' means no privacy filter for owners
      } else {
        // Non-owners can only see public content regardless of privacy parameter
        videoQuery.andWhere('v.privacy = :privacy', { privacy: 'public' });
      }

      // Apply NSFW filtering
      if (!includeNsfw) {
        videoQuery.andWhere('v.is_nsfw = :isNsfw', { isNsfw: false });
      }

      // Apply model IDs filtering for videos
      // Videos inherit models from their original image completion
      if (modelIds && modelIds.length > 0) {
        const validModelIds = this.validateModelIds(modelIds);

        if (validModelIds.length > 0) {
          videoQuery
            .leftJoin('v.originalImageCompletion', 'oic')
            .leftJoin('oic.models', 'oic_models')
            .andWhere('oic_models.modelId IN (:...validModelIds)', {
              validModelIds,
            });

          this.logger.log('Applied model filtering for videos', {
            originalModelIds: modelIds,
            validModelIds,
          });
        } else {
          // All model IDs were invalid - apply impossible filter to return 0 results
          videoQuery.andWhere('1 = 0');

          this.logger.warn(
            'All model IDs invalid for video filtering - applying impossible filter',
            {
              originalModelIds: modelIds,
            },
          );
        }
      }

      // Apply date range filtering
      if (startDate) {
        videoQuery.andWhere('v.created_at >= :startDate', { startDate });
      }
      if (endDate) {
        videoQuery.andWhere('v.created_at <= :endDate', { endDate });
      }

      // Apply cursor-based pagination
      if (cursor) {
        if (sortBy === 'createdAt') {
          // For date-based sorting, use cursor as timestamp
          const cursorDate = new Date(cursor);
          if (!isNaN(cursorDate.getTime())) {
            const operator = sortOrder.toUpperCase() === 'ASC' ? '>' : '<';
            videoQuery.andWhere(`v.created_at ${operator} :cursorDate`, {
              cursorDate: cursorDate,
            });
          }
        } else {
          // For other sorting, use cursor as timestamp fallback
          const cursorDate = new Date(cursor);
          if (!isNaN(cursorDate.getTime())) {
            videoQuery.andWhere('v.created_at < :cursorDate', {
              cursorDate: cursorDate,
            });
          }
        }
      }

      queries.push(videoQuery);
    }

    // Execute queries and combine results
    const results = await Promise.all(
      queries.map((query) => query.getRawMany()),
    );

    // Flatten and combine all results
    let allEntries = results.flat();

    this.logger.log('User content entries retrieved', {
      userId,
      totalEntries: allEntries.length,
      imageEntries: allEntries.filter((e) => e.entityType === 'image').length,
      videoEntries: allEntries.filter((e) => e.entityType === 'video').length,
    });

    // CRITICAL SECURITY: Validate ownership of all retrieved content
    const validatedEntries = await this.validateContentOwnership(
      allEntries,
      userId,
      'getUserContentEntries',
    );

    if (validatedEntries.length !== allEntries.length) {
      this.logger.error(
        'CRITICAL: Ownership violations in getUserContentEntries',
        {
          userId,
          originalCount: allEntries.length,
          validatedCount: validatedEntries.length,
          violationCount: allEntries.length - validatedEntries.length,
        },
      );
    }

    allEntries = validatedEntries;

    // Sort by the requested field
    allEntries.sort((a, b) => {
      const aValue = a[sortBy] || a.createdAt;
      const bValue = b[sortBy] || b.createdAt;

      if (sortOrder.toUpperCase() === 'ASC') {
        return new Date(aValue).getTime() - new Date(bValue).getTime();
      } else {
        return new Date(bValue).getTime() - new Date(aValue).getTime();
      }
    });

    // Apply limit and add score (limit is now handled by the caller)
    const limitedEntries = allEntries.slice(0, limit);
    return limitedEntries.map((entry) => ({
      ...entry,
      score: this.calculateContentScore(entry), // Calculate score based on engagement
    }));
  }

  /**
   * Calculate content score based on engagement metrics
   */
  private calculateContentScore(entry: any): number {
    const likes = entry.likes || 0;
    const comments = entry.comments || 0;
    const regenerations = entry.regenerations || 0;
    const reports = entry.reports || 0;

    // Calculate engagement score
    const engagementScore = likes * 2 + comments * 3 + regenerations * 1.5;

    // Calculate recency score (newer content gets higher score)
    const ageInDays =
      (Date.now() - new Date(entry.createdAt).getTime()) /
      (1000 * 60 * 60 * 24);
    const recencyScore = Math.max(0, 100 - ageInDays * 2);

    // Penalize reported content
    const reportPenalty = reports * 10;

    // Final score
    return Math.max(0, engagementScore + recencyScore - reportPenalty);
  }

  /**
   * Resolve user ID from either UUID or username
   */
  private async resolveUserId(userIdentifier: string): Promise<string> {
    // Check if the identifier is a valid UUID
    if (this.isValidUUID(userIdentifier)) {
      // Verify the user exists
      const user = await this.userProvider.findOneBy({ id: userIdentifier });
      if (!user) {
        throw new Error(`User not found with ID: ${userIdentifier}`);
      }
      return userIdentifier;
    } else {
      // Treat as username
      const user = await this.userProvider.findOneBy({
        username: userIdentifier.toLowerCase(),
      });
      if (!user) {
        throw new Error(`User not found with username: ${userIdentifier}`);
      }
      return user.id;
    }
  }

  /**
   * Check if a string is a valid UUID (v4 format)
   */
  private isValidUUID(str: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  /**
   * Get board feed (supports both boardId and board name)
   */
  async getBoardFeed(
    boardIdentifier: string,
    currentUserId?: string,
    request?: DiscoveryFeedRequestDto,
  ): Promise<FeedResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log('Generating board feed', {
        boardIdentifier,
        currentUserId,
        request,
      });

      // Resolve board ID from identifier (UUID or name)
      const boardId = await this.resolveBoardId(boardIdentifier);

      // Check board access permissions
      await this.validateBoardAccess(boardId, currentUserId);

      // Check cache first
      const cacheKey = this.buildCacheKey('board', boardId, request);
      const cachedFeed = await this.feedCacheService.get(cacheKey);

      if (cachedFeed) {
        this.logger.log('Board feed cache hit', { boardId, cacheKey });
        return this.addMetadata(cachedFeed, 'board', startTime, 'hit');
      }

      // Generate board feed
      const feedResponse = await this.generateBoardFeed(
        boardId,
        currentUserId,
        request,
      );

      // Cache the result
      await this.feedCacheService.set(cacheKey, feedResponse, 300); // 5 minutes TTL

      return this.addMetadata(feedResponse, 'board', startTime, 'miss');
    } catch (error) {
      this.logger.error('Failed to generate board feed', {
        boardIdentifier,
        currentUserId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Resolve board ID from either UUID or board name
   */
  private async resolveBoardId(boardIdentifier: string): Promise<string> {
    // Validate input
    if (!boardIdentifier || typeof boardIdentifier !== 'string') {
      throw new Error('Invalid board identifier provided');
    }

    try {
      if (this.isValidUUID(boardIdentifier)) {
        // Query by board ID
        const board = await this.boardProvider.findOneBy({
          id: boardIdentifier,
          deletedAt: null, // Ensure board is not deleted
        });
        if (!board) {
          throw new Error(`Board not found with ID: ${boardIdentifier}`);
        }
        return boardIdentifier;
      } else {
        // Query by board name (case-insensitive)
        const board = await this.boardProvider.findOneBy({
          name: boardIdentifier,
          deletedAt: null, // Ensure board is not deleted
        });
        if (!board) {
          throw new Error(`Board not found with name: ${boardIdentifier}`);
        }
        return board.id;
      }
    } catch (error) {
      this.logger.error('Error resolving board ID', {
        boardIdentifier,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Validate board access permissions
   */
  private async validateBoardAccess(
    boardId: string,
    userId: string | null,
  ): Promise<void> {
    try {
      const board = await this.boardProvider.get(boardId);

      this.logger.log('Validating board access', {
        boardId,
        userId,
        boardVisibility: board.visibility,
        boardName: board.name,
      });

      // Public boards are accessible to everyone
      if (board.visibility === VisibilityEnum.PUBLIC) {
        this.logger.log('Board access granted - public board', { boardId });
        return;
      }

      // Private boards require membership
      if (!userId) {
        this.logger.log('Board access denied - authentication required', {
          boardId,
        });
        throw new Error('Authentication required for private boards');
      }

      const isMember = await this.boardUserProvider.isMember(userId, boardId);
      if (!isMember) {
        this.logger.log('Board access denied - not a member', {
          boardId,
          userId,
        });
        throw new Error('Access denied to private board');
      }

      this.logger.log('Board access granted - member access', {
        boardId,
        userId,
      });
    } catch (error) {
      this.logger.error('Error validating board access', {
        boardId,
        userId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Generate board feed content
   */
  private async generateBoardFeed(
    boardId: string,
    userId: string | null,
    request: DiscoveryFeedRequestDto,
  ): Promise<FeedResponseDto> {
    // Get board content entries
    const boardContent = await this.getBoardContentEntries(
      boardId,
      request,
      userId,
    );

    // Apply scoring based on engagement metrics
    const scoredEntries = this.scoreBoardContent(boardContent, request);

    // Build feed response using existing infrastructure
    return this.buildFeedResponse(scoredEntries, request, userId);
  }

  /**
   * Get board content entries (images and videos)
   */
  private async getBoardContentEntries(
    boardId: string,
    request: DiscoveryFeedRequestDto,
    userId?: string,
  ): Promise<any[]> {
    const limit = Math.min(request.limit || 20, 100);
    const entityTypeFilter = request.entityType || EntityType.ALL;

    // Check if user is a board member to determine content visibility
    const isBoardMember = userId
      ? await this.isBoardMember(userId, boardId)
      : false;

    // Build queries for board content
    const queries = [];

    // Add image query if needed
    if (
      entityTypeFilter === EntityType.ALL ||
      entityTypeFilter === EntityType.IMAGE
    ) {
      const imageQuery = this.boardImageCompletionRepository
        .createQueryBuilder('bic')
        .select([
          "'image' as entityType",
          'bic.image_completion_id as entityId',
          'bic.created_at as createdAt',
          'ic.likes',
          'ic.comments',
          'ic.regenerations',
          'ic.reports',
          'ic.privacy',
          'ic.is_nsfw as isNsfw',
          'ic.status',
        ])
        .innerJoin('image_completion', 'ic', 'bic.image_completion_id = ic.id')
        .where('bic.board_id = :boardId', { boardId })
        .andWhere('bic.deleted_at IS NULL')
        .andWhere('ic.deleted_at IS NULL');

      // Apply privacy and status filters based on board membership
      if (!isBoardMember) {
        // Non-members can only see public, ready content
        imageQuery
          .andWhere('ic.privacy = :privacy', { privacy: 'public' })
          .andWhere('ic.status = :status', { status: 'ready' });
      }
      // Board members can see all content (private, public, various statuses)
      // This matches the behavior of the old board endpoints

      queries.push(imageQuery);
    }

    // Add video query if needed
    if (
      entityTypeFilter === EntityType.ALL ||
      entityTypeFilter === EntityType.VIDEO
    ) {
      const videoQuery = this.boardVideoRepository
        .createQueryBuilder('bv')
        .select([
          "'video' as entityType",
          'bv.video_id as entityId',
          'bv.created_at as createdAt',
          'v.likes',
          'v.comments',
          'v.regenerations',
          'v.reports',
          'v.privacy',
          'v.is_nsfw as isNsfw',
          'v.status',
        ])
        .innerJoin('video', 'v', 'bv.video_id = v.id')
        .where('bv.board_id = :boardId', { boardId })
        .andWhere('bv.deleted_at IS NULL')
        .andWhere('v.deleted_at IS NULL');

      // Apply privacy and status filters based on board membership
      if (!isBoardMember) {
        // 🔒 STRICT PRIVACY ENFORCEMENT: Non-members can only see public, ready content
        // This is a critical security requirement for video privacy
        videoQuery
          .andWhere('v.privacy = :privacy', { privacy: 'public' })
          .andWhere('v.status = :status', { status: 'ready' });
      } else {
        // 🔒 ENHANCED SECURITY: Even board members only see public videos in feed contexts
        // Private videos within boards should be accessed through dedicated board endpoints
        // This ensures consistent privacy enforcement across all feed endpoints
        videoQuery
          .andWhere('v.privacy = :privacy', { privacy: 'public' })
          .andWhere('v.status = :status', { status: 'ready' });
      }

      queries.push(videoQuery);
    }

    if (queries.length === 0) {
      return [];
    }

    // Execute queries separately and merge results
    const results = [];

    for (const query of queries) {
      const queryResults = await query
        .limit(limit * 2) // Get more to allow for scoring and filtering
        .getRawMany();
      results.push(...queryResults);
    }

    // Apply sorting based on request
    const sortBy = request.sortBy || SortOrder.RECENT;
    const sortOrder = request.sortOrder || SortOrderDirection.DESC;

    // Sort the combined results
    results.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case SortOrder.RECENT:
        case SortOrder.CREATED_AT:
          comparison =
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case SortOrder.ENGAGEMENT:
          const aEngagement =
            (parseInt(a.likes) || 0) +
            (parseInt(a.comments) || 0) * 2 +
            (parseInt(a.regenerations) || 0) * 0.5;
          const bEngagement =
            (parseInt(b.likes) || 0) +
            (parseInt(b.comments) || 0) * 2 +
            (parseInt(b.regenerations) || 0) * 0.5;
          comparison = aEngagement - bEngagement;
          break;
        case SortOrder.LIKES:
          comparison = (parseInt(a.likes) || 0) - (parseInt(b.likes) || 0);
          break;
        default:
          comparison =
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      }

      // Apply sort direction
      return sortOrder === SortOrderDirection.DESC ? -comparison : comparison;
    });

    // Apply final limit and normalize property names
    return results.slice(0, limit * 2).map((result) => ({
      ...result,
      entityType: result.entitytype || result.entityType,
      entityId: result.entityid || result.entityId,
      createdAt: result.createdat || result.createdAt,
    }));
  }

  /**
   * Score board content based on engagement and recency
   */
  private scoreBoardContent(
    entries: any[],
    request: DiscoveryFeedRequestDto,
  ): any[] {
    const now = Date.now();

    return entries
      .map((entry) => {
        // Calculate recency score (newer content scores higher)
        const ageInHours =
          (now - new Date(entry.createdAt).getTime()) / (1000 * 60 * 60);
        const recencyScore = Math.max(0, 100 - (ageInHours / 24) * 10); // Decay over days

        // Calculate engagement score
        const likes = parseInt(entry.likes) || 0;
        const comments = parseInt(entry.comments) || 0;
        const regenerations = parseInt(entry.regenerations) || 0;
        const engagementScore =
          likes * 1.0 + comments * 2.0 + regenerations * 0.5;

        // Calculate board relevance score (how well content fits board)
        const relevanceScore = 50; // Base relevance for board content

        // Calculate creator score (board members vs external)
        const creatorScore = 25; // Base score for board content

        // Final weighted score
        const finalScore =
          recencyScore * 0.3 +
          engagementScore * 0.4 +
          relevanceScore * 0.2 +
          creatorScore * 0.1;

        return {
          ...entry,
          score: finalScore,
          recencyScore,
          engagementScore,
          relevanceScore,
          creatorScore,
        };
      })
      .sort((a, b) => b.score - a.score) // Sort by score descending
      .slice(0, request.limit || 20); // Apply final limit
  }

  /**
   * Populate user feed entries in FeedEntryEntity table
   * This method creates pre-computed feed entries for a user based on their content
   * ENHANCED: Includes comprehensive ownership validation to prevent data isolation issues
   */
  async populateUserFeedEntries(userId: string): Promise<number> {
    try {
      this.logger.log(
        'Populating user feed entries with ownership validation',
        { userId },
      );

      // Get user's content directly from repositories
      const userContent = await this.getUserContentEntries(
        userId,
        { limit: 9999999, sortBy: 'createdAt', sortOrder: 'DESC' },
        null, // No privacy filtering for own content
      );

      // CRITICAL SECURITY: Validate ownership of all content before caching
      const validatedContent = await this.validateContentOwnership(
        userContent,
        userId,
        'cache_population',
      );

      if (validatedContent.length !== userContent.length) {
        this.logger.error(
          'CRITICAL: Ownership violations detected during cache population',
          {
            userId,
            originalCount: userContent.length,
            validatedCount: validatedContent.length,
            violationCount: userContent.length - validatedContent.length,
          },
        );
      }

      // Clear existing feed entries for this user
      await this.feedEntryRepository.delete({ userId });

      // Create new feed entries ONLY for validated content
      const feedEntries: Partial<FeedEntryEntity>[] = validatedContent.map(
        (content) => ({
          userId,
          entityType: content.entityType,
          entityId: content.entityId,
          score: content.score || this.calculateContentScore(content),
          createdAt: content.createdAt,
          expiresAt: null, // User's own content doesn't expire
        }),
      );

      if (feedEntries.length > 0) {
        await this.feedEntryRepository.save(feedEntries);
      }

      this.logger.log(
        'User feed entries populated successfully with ownership validation',
        {
          userId,
          entriesCreated: feedEntries.length,
          ownershipValidated: true,
        },
      );

      return feedEntries.length;
    } catch (error) {
      this.logger.error('Error populating user feed entries', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Refresh user feed cache (wrapper for compatibility)
   * Enhanced to also refresh FeedEntryEntity data
   */
  async refreshUserFeed(userId: string, refreshDto?: any): Promise<any> {
    try {
      const startTime = Date.now();

      // Invalidate cache
      await this.feedCacheService.invalidateUserFeed(userId);

      // Refresh pre-computed feed entries
      const itemsRefreshed = await this.populateUserFeedEntries(userId);

      // Optionally rebuild the feed immediately
      if (refreshDto?.rebuild) {
        const result = await this.getFollowingFeed(userId, {
          feedType: 'following' as any,
          limit: 20,
          sortBy: 'recent' as any,
        });
        // Don't override itemsRefreshed from populateUserFeedEntries
      }

      const durationMs = Date.now() - startTime;

      this.logger.log('User feed refreshed successfully', {
        userId,
        rebuild: refreshDto?.rebuild,
        itemsRefreshed,
        durationMs,
      });

      return {
        success: true,
        itemsRefreshed,
        durationMs,
        refreshedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to refresh user feed', {
        error: error.message,
        userId,
        refreshDto,
      });
      throw error;
    }
  }

  /**
   * CRITICAL SECURITY: Validate content ownership to prevent data isolation issues
   * This method ensures that all content belongs to the expected user
   */
  private async validateContentOwnership(
    content: any[],
    expectedUserId: string,
    context: string,
  ): Promise<any[]> {
    try {
      this.logger.log('Validating content ownership', {
        contentCount: content.length,
        expectedUserId,
        context,
      });

      const validatedContent: any[] = [];
      const violations: any[] = [];

      for (const item of content) {
        let actualUserId: string | null = null;
        let isValid = false;

        // Extract user ID based on content type
        if (item.entityType === 'image') {
          // For direct database results, userId might be in different fields
          actualUserId = item.userId || item.user_id || item.ownerId;
        } else if (item.entityType === 'video') {
          actualUserId = item.userId || item.user_id || item.ownerId;
        }

        // Validate ownership
        isValid = actualUserId === expectedUserId;

        if (isValid) {
          validatedContent.push(item);
        } else {
          violations.push({
            entityType: item.entityType,
            entityId: item.entityId,
            expectedUserId,
            actualUserId,
            context,
          });
        }
      }

      if (violations.length > 0) {
        this.logger.error('CRITICAL: Content ownership violations detected', {
          context,
          expectedUserId,
          totalContent: content.length,
          validContent: validatedContent.length,
          violationCount: violations.length,
          violations: violations.slice(0, 10), // Log first 10 violations
        });
      }

      return validatedContent;
    } catch (error) {
      this.logger.error('Error validating content ownership', {
        context,
        expectedUserId,
        error: error.message,
      });
      // In case of error, return empty array to be safe
      return [];
    }
  }

  /**
   * CRITICAL SECURITY: Validate unified entities ownership
   */
  private async validateUnifiedEntitiesOwnership(
    entities: any[],
    expectedUserId: string,
    context: string,
  ): Promise<{
    validEntities: any[];
    violations: any[];
    violationCount: number;
  }> {
    try {
      this.logger.log('Validating unified entities ownership', {
        entitiesCount: entities.length,
        expectedUserId,
        context,
      });

      const validEntities: any[] = [];
      const violations: any[] = [];

      for (const entity of entities) {
        let actualUserId: string | null = null;
        let isValid = false;

        // Extract user ID from unified entity
        if (entity.owner?.id) {
          actualUserId = entity.owner.id;
        }

        // Validate ownership
        isValid = actualUserId === expectedUserId;

        if (isValid) {
          validEntities.push(entity);
        } else {
          violations.push({
            entityType: entity.entityType,
            entityId: entity.entityId,
            expectedUserId,
            actualUserId,
            context,
          });
        }
      }

      if (violations.length > 0) {
        this.logger.error(
          'CRITICAL: Unified entities ownership violations detected',
          {
            context,
            expectedUserId,
            totalEntities: entities.length,
            validEntities: validEntities.length,
            violationCount: violations.length,
            violations: violations.slice(0, 10), // Log first 10 violations
          },
        );
      }

      return {
        validEntities,
        violations,
        violationCount: violations.length,
      };
    } catch (error) {
      this.logger.error('Error validating unified entities ownership', {
        context,
        expectedUserId,
        error: error.message,
      });
      // In case of error, return empty arrays to be safe
      return {
        validEntities: [],
        violations: [],
        violationCount: 0,
      };
    }
  }

  /**
   * Validate that feed entries correspond to existing entities
   * This helps identify orphaned entries before they cause "Entity not found" errors
   */
  async validateFeedEntries(
    entries: Array<{ entityType: string; entityId: string }>,
  ): Promise<Array<{ entityType: string; entityId: string; exists: boolean }>> {
    try {
      const imageIds = entries
        .filter((e) => e.entityType === 'image')
        .map((e) => e.entityId);
      const videoIds = entries
        .filter((e) => e.entityType === 'video')
        .map((e) => e.entityId);

      // Check which images exist
      const existingImages =
        imageIds.length > 0
          ? await this.imageCompletionRepository.find({
              where: { id: In(imageIds), deletedAt: null },
              select: ['id'],
            })
          : [];

      // Check which videos exist
      const existingVideos =
        videoIds.length > 0
          ? await this.videoRepository.find({
              where: { id: In(videoIds), deletedAt: null },
              select: ['id'],
            })
          : [];

      const existingImageIds = new Set(existingImages.map((img) => img.id));
      const existingVideoIds = new Set(existingVideos.map((video) => video.id));

      // Map each entry to its existence status
      return entries.map((entry) => ({
        entityType: entry.entityType,
        entityId: entry.entityId,
        exists:
          entry.entityType === 'image'
            ? existingImageIds.has(entry.entityId)
            : existingVideoIds.has(entry.entityId),
      }));
    } catch (error) {
      this.logger.error('Error validating feed entries', {
        error: error.message,
        entriesCount: entries.length,
      });
      return entries.map((entry) => ({ ...entry, exists: false }));
    }
  }

  /**
   * Clean up orphaned feed entries that reference non-existent entities
   * This method removes feed entries where the referenced entity no longer exists
   */
  async cleanupOrphanedFeedEntries(userId?: string): Promise<number> {
    try {
      this.logger.log('Starting cleanup of orphaned feed entries', {
        userId,
      });

      // Build base query
      let queryBuilder = this.feedEntryRepository
        .createQueryBuilder('fe')
        .leftJoin(
          'image_completion',
          'ic',
          'fe.entityType = :imageType AND fe.entityId = ic.id AND ic.deletedAt IS NULL',
          { imageType: 'image' },
        )
        .leftJoin(
          'video',
          'v',
          'fe.entityType = :videoType AND fe.entityId = v.id AND v.deletedAt IS NULL',
          { videoType: 'video' },
        )
        .where(
          '(fe.entityType = :imageType2 AND ic.id IS NULL) OR (fe.entityType = :videoType2 AND v.id IS NULL)',
          {
            imageType2: 'image',
            videoType2: 'video',
          },
        );

      // Optionally filter by user
      if (userId) {
        queryBuilder = queryBuilder.andWhere('fe.userId = :userId', { userId });
      }

      // Find orphaned entries
      const orphanedEntries = await queryBuilder.getMany();

      if (orphanedEntries.length > 0) {
        // Remove orphaned entries
        await this.feedEntryRepository.remove(orphanedEntries);

        this.logger.log('Cleaned up orphaned feed entries', {
          userId,
          removedCount: orphanedEntries.length,
          orphanedIds: orphanedEntries
            .slice(0, 10)
            .map((e) => ({ entityType: e.entityType, entityId: e.entityId })),
        });
      } else {
        this.logger.log('No orphaned feed entries found', { userId });
      }

      return orphanedEntries.length;
    } catch (error) {
      this.logger.error('Error cleaning up orphaned feed entries', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      return 0;
    }
  }

  /**
   * Add new content to user's feed entries
   * Called when a user creates new content (image or video)
   */
  async addContentToUserFeed(
    userId: string,
    entityType: string,
    entityId: string,
    baseScore?: number,
  ): Promise<void> {
    try {
      const score = baseScore || 100; // Default score for new content

      const feedEntry: Partial<FeedEntryEntity> = {
        userId,
        entityType,
        entityId,
        score,
        expiresAt: null, // User's own content doesn't expire
      };

      await this.feedEntryRepository.save(feedEntry);

      this.logger.log('Added new content to user feed entries', {
        userId,
        entityType,
        entityId,
        score,
      });

      // Invalidate user's feed cache to reflect new content
      await this.feedCacheService.invalidateUserFeed(userId);
    } catch (error) {
      this.logger.error('Error adding content to user feed', {
        userId,
        entityType,
        entityId,
        error: error.message,
      });
      // Don't throw error to avoid breaking content creation
    }
  }

  /**
   * Refresh global feed cache with high-engagement content (images and videos)
   */
  async refreshGlobalCache(): Promise<any> {
    try {
      const startTime = Date.now();
      this.logger.log('Starting global feed cache refresh');

      // Add images with high regenerations to the cache
      const imageResult = await this.globalFeedCacheRepository.query(`
        WITH upsert_result AS (
          INSERT INTO global_feed_cache (entity_type, entity_id, global_score, engagement_score, recency_score)
          SELECT
            'image' as entity_type,
            ic.id as entity_id,
            (ic.regenerations * 10 + 50) as global_score,
            ic.regenerations * 5 as engagement_score,
            0.8 as recency_score
          FROM image_completion ic
          WHERE ic.deleted_at IS NULL
            AND ic.privacy = 'public'
            AND ic.status = 'ready'
          ON CONFLICT (entity_type, entity_id) DO UPDATE SET
            global_score = EXCLUDED.global_score,
            engagement_score = EXCLUDED.engagement_score,
            recency_score = EXCLUDED.recency_score,
            updated_at = NOW()
          RETURNING 1
        )
        SELECT COUNT(*) as items_processed FROM upsert_result
      `);

      // Add videos with high engagement to the cache
      const videoResult = await this.globalFeedCacheRepository.query(`
        WITH upsert_result AS (
          INSERT INTO global_feed_cache (entity_type, entity_id, global_score, engagement_score, recency_score)
          SELECT
            'video' as entity_type,
            v.id as entity_id,
            (COALESCE(v.likes, 0) * 15 + COALESCE(v.comments, 0) * 25 + COALESCE(v.regenerations, 0) * 10 + 60) as global_score,
            (COALESCE(v.likes, 0) * 2 + COALESCE(v.comments, 0) * 3 + COALESCE(v.regenerations, 0)) as engagement_score,
            CASE
              WHEN v.created_at > NOW() - INTERVAL '24 hours' THEN 1.0
              WHEN v.created_at > NOW() - INTERVAL '7 days' THEN 0.8
              WHEN v.created_at > NOW() - INTERVAL '30 days' THEN 0.6
              ELSE 0.4
            END as recency_score
          FROM video v
          WHERE v.deleted_at IS NULL
            AND v.privacy = 'public'
            AND v.status = 'ready'
          ON CONFLICT (entity_type, entity_id) DO UPDATE SET
            global_score = EXCLUDED.global_score,
            engagement_score = EXCLUDED.engagement_score,
            recency_score = EXCLUDED.recency_score,
            updated_at = NOW()
          RETURNING 1
        )
        SELECT COUNT(*) as items_processed FROM upsert_result
      `);

      const durationMs = Date.now() - startTime;

      // Extract the actual number of processed items from both queries
      const imagesProcessed = imageResult?.[0]?.items_processed || 0;
      const videosProcessed = videoResult?.[0]?.items_processed || 0;
      const totalItemsProcessed = imagesProcessed + videosProcessed;

      this.logger.log('Global feed cache refreshed successfully', {
        durationMs,
        imagesProcessed,
        videosProcessed,
        totalItemsProcessed,
      });

      return {
        success: true,
        itemsProcessed: totalItemsProcessed,
        imagesProcessed,
        videosProcessed,
        durationMs,
        refreshedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to refresh global feed cache', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }
}
