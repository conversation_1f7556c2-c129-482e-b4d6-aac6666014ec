import { Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { FeedResponseDto } from '../dto/feed-response.dto';

/**
 * Feed caching service with Redis integration and TTL management
 * Handles caching of feed responses to improve performance
 */
@Injectable()
export class FeedCacheService {
  private readonly cachePrefix = 'feed:';
  private readonly defaultTtl = 300; // 5 minutes default TTL

  constructor(private readonly logger: Logger) {}

  /**
   * Get cached feed response with ownership validation
   * ENHANCED: Includes ownership validation for user feeds to prevent data isolation issues
   */
  async get(
    key: string,
    expectedUserId?: string,
  ): Promise<FeedResponseDto | null> {
    try {
      const cacheKey = this.buildCacheKey(key);

      // TODO: Implement Redis integration
      // For now, return null to indicate cache miss
      // const cached = await this.redisClient.get(cacheKey);
      // if (cached) {
      //   const feedResponse = JSON.parse(cached);
      //
      //   // CRITICAL SECURITY: Validate ownership if this is a user feed
      //   if (expectedUserId && this.isUserFeedKey(key)) {
      //     const validatedResponse = await this.validateCachedFeedOwnership(feedResponse, expectedUserId);
      //     return validatedResponse;
      //   }
      //
      //   return feedResponse;
      // }

      this.logger.debug('Feed cache miss', { key: cacheKey, expectedUserId });
      return null;
    } catch (error) {
      this.logger.error('Failed to get from feed cache', {
        key,
        expectedUserId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Set cached feed response with TTL
   */
  async set(
    key: string,
    value: FeedResponseDto,
    ttlSeconds?: number,
  ): Promise<void> {
    try {
      const cacheKey = this.buildCacheKey(key);
      const ttl = ttlSeconds || this.defaultTtl;

      // TODO: Implement Redis integration
      // await this.redisClient.setex(cacheKey, ttl, JSON.stringify(value));

      this.logger.debug('Feed cached successfully', {
        key: cacheKey,
        ttl,
        itemCount: value.items.length,
      });
    } catch (error) {
      this.logger.error('Failed to set feed cache', {
        key,
        error: error.message,
      });
    }
  }

  /**
   * Delete cached feed response
   */
  async delete(key: string): Promise<void> {
    try {
      const cacheKey = this.buildCacheKey(key);

      // TODO: Implement Redis integration
      // await this.redisClient.del(cacheKey);

      this.logger.debug('Feed cache deleted', { key: cacheKey });
    } catch (error) {
      this.logger.error('Failed to delete from feed cache', {
        key,
        error: error.message,
      });
    }
  }

  /**
   * Invalidate user's feed cache
   */
  async invalidateUserFeed(userId: string): Promise<void> {
    try {
      const patterns = [
        `${this.cachePrefix}feed:following:${userId}:*`,
        `${this.cachePrefix}feed:recommended:${userId}:*`,
      ];

      for (const pattern of patterns) {
        await this.deleteByPattern(pattern);
      }

      this.logger.debug('User feed cache invalidated', { userId });
    } catch (error) {
      this.logger.error('Failed to invalidate user feed cache', {
        userId,
        error: error.message,
      });
    }
  }

  /**
   * Invalidate discovery feed cache
   */
  async invalidateDiscoveryFeed(): Promise<void> {
    try {
      const pattern = `${this.cachePrefix}feed:discovery:*`;
      await this.deleteByPattern(pattern);

      this.logger.debug('Discovery feed cache invalidated');
    } catch (error) {
      this.logger.error('Failed to invalidate discovery feed cache', {
        error: error.message,
      });
    }
  }

  /**
   * Invalidate cache for specific entity
   */
  async invalidateEntityCache(
    entityType: string,
    entityId: string,
  ): Promise<void> {
    try {
      // Invalidate all feeds that might contain this entity
      const patterns = [
        `${this.cachePrefix}feed:*:${entityType}:*`,
        `${this.cachePrefix}feed:*:all:*`,
      ];

      for (const pattern of patterns) {
        await this.deleteByPattern(pattern);
      }

      this.logger.debug('Entity cache invalidated', { entityType, entityId });
    } catch (error) {
      this.logger.error('Failed to invalidate entity cache', {
        entityType,
        entityId,
        error: error.message,
      });
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: number;
    hitRate: number;
  }> {
    try {
      // TODO: Implement Redis integration for stats
      // const info = await this.redisClient.info('memory');
      // const keyCount = await this.redisClient.dbsize();

      return {
        totalKeys: 0,
        memoryUsage: 0,
        hitRate: 0,
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats', {
        error: error.message,
      });

      return {
        totalKeys: 0,
        memoryUsage: 0,
        hitRate: 0,
      };
    }
  }

  /**
   * Warm up cache for popular feeds
   */
  async warmUpCache(): Promise<void> {
    try {
      this.logger.log('Starting feed cache warm-up');

      // TODO: Implement cache warm-up logic
      // - Pre-generate discovery feeds for different entity types
      // - Pre-generate feeds for active users
      // - Cache popular content

      this.logger.log('Feed cache warm-up completed');
    } catch (error) {
      this.logger.error('Failed to warm up cache', {
        error: error.message,
      });
    }
  }

  /**
   * CRITICAL SECURITY: Check if cache key is for a user feed
   */
  private isUserFeedKey(key: string): boolean {
    return key.includes('user:') || key.includes('feed:user:');
  }

  /**
   * CRITICAL SECURITY: Validate cached feed ownership
   */
  private async validateCachedFeedOwnership(
    feedResponse: any,
    expectedUserId: string,
  ): Promise<any | null> {
    try {
      if (!feedResponse?.items || !Array.isArray(feedResponse.items)) {
        return feedResponse;
      }

      const violations: any[] = [];
      const validItems: any[] = [];

      for (const item of feedResponse.items) {
        let actualUserId: string | null = null;
        let isValid = false;

        // Extract user ID from feed item
        if (item.imageCompletion?.user?.id) {
          actualUserId = item.imageCompletion.user.id;
        } else if (item.imageCompletion?.userId) {
          actualUserId = item.imageCompletion.userId;
        } else if (item.video?.user?.id) {
          actualUserId = item.video.user.id;
        } else if (item.video?.userId) {
          actualUserId = item.video.userId;
        }

        // Validate ownership
        isValid = actualUserId === expectedUserId;

        if (isValid) {
          validItems.push(item);
        } else {
          violations.push({
            itemType: item.type,
            entityId: item.entityId,
            expectedUserId,
            actualUserId,
            context: 'cached_feed_validation',
          });
        }
      }

      if (violations.length > 0) {
        this.logger.error(
          'CRITICAL: Cached feed ownership violations detected',
          {
            expectedUserId,
            totalItems: feedResponse.items.length,
            validItems: validItems.length,
            violationCount: violations.length,
            violations: violations.slice(0, 5), // Log first 5 violations
          },
        );

        // Return filtered response
        return {
          ...feedResponse,
          items: validItems,
          pagination: {
            ...feedResponse.pagination,
            count: validItems.length,
          },
        };
      }

      return feedResponse;
    } catch (error) {
      this.logger.error('Error validating cached feed ownership', {
        expectedUserId,
        error: error.message,
      });
      // Return null to force cache miss and regeneration
      return null;
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredEntries(): Promise<number> {
    try {
      // TODO: Implement Redis integration for cleanup
      // Redis handles TTL automatically, but we might want to track cleanup stats

      const cleanedCount = 0;
      this.logger.debug('Cache cleanup completed', { cleanedCount });

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired cache entries', {
        error: error.message,
      });
      return 0;
    }
  }

  /**
   * Build cache key with prefix
   */
  private buildCacheKey(key: string): string {
    return `${this.cachePrefix}${key}`;
  }

  /**
   * Delete cache entries by pattern
   */
  private async deleteByPattern(pattern: string): Promise<void> {
    try {
      // TODO: Implement Redis integration
      // const keys = await this.redisClient.keys(pattern);
      // if (keys.length > 0) {
      //   await this.redisClient.del(...keys);
      // }

      this.logger.debug('Cache pattern deleted', { pattern });
    } catch (error) {
      this.logger.error('Failed to delete cache by pattern', {
        pattern,
        error: error.message,
      });
    }
  }

  /**
   * Check if cache is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      // TODO: Implement Redis health check
      // await this.redisClient.ping();
      return true;
    } catch (error) {
      this.logger.error('Cache health check failed', {
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Get cache key for feed request
   */
  buildFeedCacheKey(
    feedType: string,
    userId: string | null,
    entityType: string,
    sortBy: string,
    limit: number,
    cursor?: number,
  ): string {
    const keyParts = [
      'feed',
      feedType,
      userId || 'anonymous',
      entityType,
      sortBy,
      limit.toString(),
      cursor?.toString() || '',
    ];

    return keyParts.filter(Boolean).join(':');
  }

  /**
   * Invalidate cache based on social engagement
   */
  async invalidateOnEngagement(
    entityType: string,
    entityId: string,
    userId: string,
  ): Promise<void> {
    try {
      // Invalidate user's personalized feeds
      await this.invalidateUserFeed(userId);

      // Invalidate entity-specific cache
      await this.invalidateEntityCache(entityType, entityId);

      // Invalidate discovery feed if engagement is significant
      await this.invalidateDiscoveryFeed();

      this.logger.debug('Cache invalidated on engagement', {
        entityType,
        entityId,
        userId,
      });
    } catch (error) {
      this.logger.error('Failed to invalidate cache on engagement', {
        entityType,
        entityId,
        userId,
        error: error.message,
      });
    }
  }

  /**
   * Batch invalidate multiple cache keys
   */
  async batchInvalidate(keys: string[]): Promise<void> {
    try {
      const cacheKeys = keys.map((key) => this.buildCacheKey(key));

      // TODO: Implement Redis batch deletion
      // if (cacheKeys.length > 0) {
      //   await this.redisClient.del(...cacheKeys);
      // }

      this.logger.debug('Batch cache invalidation completed', {
        keyCount: cacheKeys.length,
      });
    } catch (error) {
      this.logger.error('Failed to batch invalidate cache', {
        keyCount: keys.length,
        error: error.message,
      });
    }
  }
}
