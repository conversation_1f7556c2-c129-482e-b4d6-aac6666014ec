import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { SearchIndexService } from '../../search/service/search-index.service';
import { EntityAggregatorService } from '../service/entity-aggregator.service';

/**
 * Listener for content publishing events that affect search indexing
 * Feed-related logic is handled by ContentPublishedListener in the feed module
 */
@Injectable()
export class SocialEngagementListener {
  constructor(
    private readonly searchIndexService: SearchIndexService,
    private readonly entityAggregatorService: EntityAggregatorService,
    private readonly logger: Logger,
  ) {}

  /**
   * Handle image published events for search indexing
   */
  @OnEvent('image.published')
  async handleImagePublishedEvent(event: any): Promise<void> {
    try {
      this.logger.debug('Handling image published event for search indexing', {
        event,
      });

      // Update search index for published content
      await this.searchIndexService.updateEntityIndex('image', event.id, true);

      this.logger.log(
        'Image published event processed successfully for search',
        {
          imageId: event.id,
          userId: event.userId,
        },
      );
    } catch (error) {
      this.logger.error('Failed to handle image published event for search', {
        event,
        error: error.message,
        stack: error.stack,
      });
      // Don't throw error to avoid breaking image publishing process
    }
  }

  /**
   * Handle video published events for search indexing
   */
  @OnEvent('video.published')
  async handleVideoPublishedEvent(event: any): Promise<void> {
    try {
      this.logger.debug('Handling video published event for search indexing', {
        event,
      });

      // Update search index for published content
      await this.searchIndexService.updateEntityIndex('video', event.id, true);

      this.logger.log(
        'Video published event processed successfully for search',
        {
          videoId: event.id,
          userId: event.userId,
        },
      );
    } catch (error) {
      this.logger.error('Failed to handle video published event for search', {
        event,
        error: error.message,
        stack: error.stack,
      });
      // Don't throw error to avoid breaking video publishing process
    }
  }
}
