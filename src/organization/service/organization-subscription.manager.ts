import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { Logger } from 'nestjs-pino';
import { CreditPackageEntity } from 'src/subscription/entity/credit-package.entity';
import { CreditPackageManager } from 'src/subscription/service/credit-package.manager';
import Stripe from 'stripe';
import { Repository } from 'typeorm';
import { CreditTypeEnum } from '../../subscription/entity/credit-type.enum';
import { StripeService } from '../../subscription/service/stripe.service';
import { UserCreditBalanceManager } from '../../subscription/service/user-credit-balance.manager';
import { OrganizationEntity } from '../entity/organization.entity';
import { OrganizationSubscriptionEntity } from '../entity/organization-subscription.entity';
import { SubscriptionStatusEnum } from '../enum/subscription-status.enum';
import { OrganizationManager } from './manager';
import { OrganizationSubscriptionProvider } from './organization-subscription.provider';
import { OrganizationUserProvider } from './organization-user.provider';

@Injectable()
export class OrganizationSubscriptionManager {
  constructor(
    @InjectRepository(OrganizationSubscriptionEntity)
    private readonly repository: Repository<OrganizationSubscriptionEntity>,
    @InjectRepository(OrganizationEntity)
    private readonly organizationRepository: Repository<OrganizationEntity>,
    @Inject('Stripe') private readonly stripe: Stripe,
    private readonly stripeService: StripeService,
    private readonly userCreditBalanceManager: UserCreditBalanceManager,
    private readonly configService: ConfigService,
    private readonly logger: Logger,
    private readonly provider: OrganizationSubscriptionProvider,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private readonly organizationManager: OrganizationManager,
    private readonly creditPackageManager: CreditPackageManager,
  ) {}

  async create(
    subscription: OrganizationSubscriptionEntity,
  ): Promise<OrganizationSubscriptionEntity> {
    const pendingSubscriptions =
      await this.provider.findPendingSubscriptionsForOrganization(
        subscription.organization.id,
      );

    if (pendingSubscriptions.length > 0) {
      throw new BadRequestException('subscription.pending');
    }

    subscription.status = SubscriptionStatusEnum.NEW;
    await this.repository.save(subscription);

    try {
      if (subscription.price > 0) {
        await this.createStripeCheckout(subscription);
      } else {
        await this.activateSubscription(subscription);
      }

      return subscription;
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to create organization subscription', {
        error,
        organizationId: subscription.organization.id,
      });
      throw new BadRequestException('subscription.creation_failed');
    }
  }

  async activateSubscription(
    subscription: OrganizationSubscriptionEntity,
  ): Promise<void> {
    if (
      subscription.status == SubscriptionStatusEnum.ACTIVE ||
      subscription.status == SubscriptionStatusEnum.INACTIVE
    ) {
      return;
    }

    const creditPackage = subscription.creditPackage;

    try {
      for (const [creditType, amount] of Object.entries(
        creditPackage.creditTypes,
      )) {
        // Calculate credit expiration date
        // For yearly subscriptions, credits expire monthly, not at subscription end
        const creditExpirationDate =
          this.calculateCreditExpirationDate(subscription);

        await this.userCreditBalanceManager.increase(
          creditType.toLowerCase() as CreditTypeEnum,
          amount,
          null,
          subscription.organization.id,
          creditExpirationDate,
        );
      }
      subscription.status = SubscriptionStatusEnum.ACTIVE;
      await this.repository.save(subscription);
      const seats = creditPackage.seats ?? 0; // Fallback to 0 if seats is null or undefined
      if (subscription.organization.seatsPurchased < seats) {
        await this.organizationManager.updateSeats(
          subscription.organization,
          seats,
        );
      }
    } catch (error) {
      this.logger.error('Failed to activate organization subscription', {
        error,
        organizationId: subscription.organization.id,
      });
      throw new BadRequestException('subscription.activation_failed');
    }
  }

  async updateStripeCheckoutStatus(
    subscription: OrganizationSubscriptionEntity,
  ): Promise<void> {
    if (!subscription.stripeCheckoutSessionId) {
      return;
    }

    try {
      const session = await this.stripe.checkout.sessions.retrieve(
        subscription.stripeCheckoutSessionId,
      );

      this.logger.log('stripe.checkout_session_status', {
        session,
        subscriptionId: subscription.id,
      });

      if (session.payment_status === 'paid') {
        const paidAt = new Date(session.created * 1000);

        subscription.paidAt = paidAt;
        subscription.stripeCheckoutUrl = null;

        if (session.subscription) {
          subscription.externalReference = session.subscription as string;
        }
        await this.activateSubscription(subscription);
      }
    } catch (error) {
      this.logger.error('Failed to update Stripe checkout status', {
        error,
        organizationId: subscription.organization.id,
        checkoutSessionId: subscription.stripeCheckoutSessionId,
      });
      throw new BadRequestException('subscription.stripe_update_failed');
    }
  }

  /**
   * Calculate credit expiration date based on subscription type
   * For yearly subscriptions, credits expire monthly
   * For monthly subscriptions, credits expire at subscription end
   */
  private calculateCreditExpirationDate(
    subscription: OrganizationSubscriptionEntity,
  ): Date {
    const isYearlySubscription =
      subscription.creditPackage.expiresAfterMonths === 12;

    if (isYearlySubscription) {
      // For yearly subscriptions, credits expire monthly (adding 1 hour to handle Stripe gap)
      const baseDate = subscription.paidAt || subscription.createdAt;
      return DateTime.fromJSDate(baseDate)
        .plus({ months: 1, hours: 1 })
        .toJSDate();
    } else {
      // For monthly subscriptions, credits expire at subscription end
      return subscription.expiresAt;
    }
  }

  private async getOrCreateStripePriceId(
    creditPackage: CreditPackageEntity,
  ): Promise<string> {
    // Use the StripeService for consistent price creation
    return this.stripeService.getOrCreateStripePriceId(creditPackage);
  }

  private async getOrCreateOrganizationStripeCustomerId(
    organization: OrganizationEntity,
    ownerEmail: string,
  ): Promise<string> {
    if (organization.stripeCustomerId) {
      return organization.stripeCustomerId;
    }

    const stripeCustomer = await this.stripe.customers.create({
      email: ownerEmail,
      name: organization.name,
      metadata: {
        organizationId: organization.id,
        type: 'organization',
      },
    });

    // Use direct repository update to avoid cascade issues
    await this.organizationRepository.update(organization.id, {
      stripeCustomerId: stripeCustomer.id,
    });

    return stripeCustomer.id;
  }

  private async createStripeCheckout(
    subscription: OrganizationSubscriptionEntity,
  ): Promise<void> {
    try {
      const organization = subscription.organization;
      const owner = (
        await this.organizationUserProvider.getOwner(organization.id)
      ).user;

      const stripePriceId = await this.getOrCreateStripePriceId(
        subscription.creditPackage,
      );

      const frontendUrl = this.configService.get<string>('FRONTEND_URL');

      const checkoutSessionConfig: Stripe.Checkout.SessionCreateParams = {
        mode: subscription.creditPackage.isRecurring
          ? 'subscription'
          : 'payment',
        success_url: `${frontendUrl}/paymentsuccess?organizationId=${organization.id}&subscriptionId=${subscription.id}`,
        cancel_url: `${frontendUrl}/paymentfailure?organizationId=${organization.id}&subscriptionId=${subscription.id}`,
        billing_address_collection: 'required',
        allow_promotion_codes: true,
        automatic_tax: {
          enabled: true,
        },
        customer_update: {
          address: 'auto',
          name: 'auto',
        },
        tax_id_collection: {
          enabled: true,
        },
        line_items: [
          {
            price: stripePriceId,
            quantity: 1,
          },
        ],
        metadata: {
          organizationId: organization.id,
          subscriptionId: subscription.id,
        },
      };

      // Get or create organization Stripe customer ID
      const organizationStripeCustomerId =
        await this.getOrCreateOrganizationStripeCustomerId(
          organization,
          owner.email,
        );
      checkoutSessionConfig.customer = organizationStripeCustomerId;

      const checkoutSession = await this.stripe.checkout.sessions.create(
        checkoutSessionConfig,
      );

      subscription.stripeCheckoutUrl = checkoutSession.url;
      subscription.stripeCheckoutSessionId = checkoutSession.id;
      await this.repository.save(subscription);
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to create Stripe checkout session', {
        error,
        organizationId: subscription.organization.id,
      });
      throw new BadRequestException('subscription.stripe_checkout_failed');
    }
  }

  async cancelSubscription(
    subscription: OrganizationSubscriptionEntity,
  ): Promise<void> {
    try {
      if (subscription.externalReference) {
        await this.stripe.subscriptions.cancel(subscription.externalReference);
      }

      subscription.status = SubscriptionStatusEnum.INACTIVE;
      subscription.cancelledAt = new Date();

      await this.repository.save(subscription);

      this.logger.log('subscription.cancelled', {
        subscriptionId: subscription.id,
        organizationId: subscription.organization.id,
      });
    } catch (error) {
      this.logger.error('Failed to cancel subscription', {
        error,
        subscriptionId: subscription.id,
      });
      throw new BadRequestException('subscription.cancellation_failed');
    }
  }
}
