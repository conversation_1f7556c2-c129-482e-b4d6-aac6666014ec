import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  Request,
  HttpCode,
  ParseUUIDPipe,
  UseGuards,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { EntityLikeProvider } from '../service/entity-like.provider';
import { EntityLikeRequestManager } from '../service/entity-like.request-manager';
import { EntityLikeResponseMapper } from '../service/entity-like.response-mapper';
import { EntityValidatorService } from '../service/entity-validator.service';
import { EntityLikeSearchRequest } from '../dto/entity-like.search.request';
import { EntityLikeDto } from '../dto/entity-like.dto';

@ApiTags('Entity Likes')
@Controller('social/:entityType/:entityId/likes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EntityLikeController {
  constructor(
    private provider: EntityLikeProvider,
    private requestManager: EntityLikeRequestManager,
    private responseMapper: EntityLikeResponseMapper,
    private validatorService: EntityValidatorService,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'entity_likes_list',
    summary: 'Get likes for an entity',
    description: 'Retrieve paginated likes for a specific entity.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Likes retrieved successfully',
    type: [EntityLikeDto],
  })
  async findLikes(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Query() query: EntityLikeSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    // Validate entity exists
    await this.validatorService.validateEntityExists(entityType, entityId);

    // Get likes
    const likes = await this.requestManager.getLikesForEntity(
      entityType,
      entityId,
      query.page,
      query.limit,
      query.sortBy,
      query.sortOrder,
    );

    // Get total count for pagination
    const total = await this.requestManager.countLikesForEntity(
      entityType,
      entityId,
    );

    // Map response with pagination
    const response = await this.responseMapper.mapWithPagination(
      likes,
      total,
      query.page,
      query.limit,
      query.includeEntity,
    );

    // Set pagination headers
    res.set({
      'X-Total-Count': total.toString(),
      'X-Page': query.page.toString(),
      'X-Limit': query.limit.toString(),
      'X-Total-Pages': response.pagination.totalPages.toString(),
    });

    res.json(response.data);
  }

  @Post()
  @HttpCode(201)
  @ApiOperation({
    operationId: 'entity_like_create',
    summary: 'Like an entity',
    description: 'Add a like to the specified entity.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 201,
    description: 'Entity liked successfully',
    type: EntityLikeDto,
  })
  async likeEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Request() request,
  ): Promise<EntityLikeDto> {
    // Get entity validator
    const entityValidator =
      this.validatorService.createEntityValidator(entityType);

    // Like entity
    const like = await this.requestManager.likeEntity(
      entityType,
      entityId,
      'request.user.id',
      entityValidator,
    );

    // Map and return response
    return this.responseMapper.map(like, false);
  }

  @Delete()
  @HttpCode(204)
  @ApiOperation({
    operationId: 'entity_like_delete',
    summary: 'Unlike an entity',
    description: 'Remove like from the specified entity.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Entity unliked successfully',
  })
  async unlikeEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Request() request,
  ): Promise<void> {
    // Get entity validator
    const entityValidator =
      this.validatorService.createEntityValidator(entityType);

    // Unlike entity
    await this.requestManager.unlikeEntity(
      entityType,
      entityId,
      request.user.id,
      entityValidator,
    );
  }

  @Post('toggle')
  @HttpCode(200)
  @ApiOperation({
    operationId: 'entity_like_toggle',
    summary: 'Toggle like status for an entity',
    description: 'Like the entity if not liked, unlike if already liked.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Like status toggled successfully',
  })
  async toggleLike(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Request() request,
  ): Promise<{ liked: boolean; like?: EntityLikeDto }> {
    // Get entity validator
    const entityValidator =
      this.validatorService.createEntityValidator(entityType);

    // Toggle like
    const result = await this.requestManager.toggleEntityLike(
      entityType,
      entityId,
      request.user.id,
      entityValidator,
    );

    // Map response
    const response: { liked: boolean; like?: EntityLikeDto } = {
      liked: result.liked,
    };

    if (result.like) {
      response.like = await this.responseMapper.map(result.like, false);
    }

    return response;
  }

  @Get('status')
  @ApiOperation({
    operationId: 'entity_like_status',
    summary: 'Check if user has liked an entity',
    description:
      'Check whether the current user has liked the specified entity.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Like status retrieved successfully',
  })
  async getLikeStatus(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Request() request,
  ): Promise<{ liked: boolean }> {
    // Check like status
    const liked = await this.requestManager.hasUserLikedEntity(
      entityType,
      entityId,
      request.user.id,
    );

    return { liked };
  }
}
