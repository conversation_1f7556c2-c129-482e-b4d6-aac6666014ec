import { Test, TestingModule } from '@nestjs/testing';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { EntityLikeNotificationService } from '../notification/entity-like.notification';
import { EntityCommentNotificationService } from '../notification/entity-comment.notification';
import { EntityValidatorService } from '../service/entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { Notifier } from '../../notification/service/notifier';

describe('Social Notification Integration', () => {
  let entityLikeNotificationService: EntityLikeNotificationService;
  let entityCommentNotificationService: EntityCommentNotificationService;
  let mockNotifier: jest.Mocked<Notifier>;
  let mockEntityValidatorService: jest.Mocked<EntityValidatorService>;
  let mockUserProvider: jest.Mocked<UserProvider>;

  beforeEach(async () => {
    // Create mocks
    mockNotifier = {
      dispatch: jest.fn(),
      mapUserChannels: jest.fn(),
    } as any;

    mockEntityValidatorService = {
      getEntityInfo: jest.fn(),
    } as any;

    mockUserProvider = {
      get: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityLikeNotificationService,
        EntityCommentNotificationService,
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
        {
          provide: EntityValidatorService,
          useValue: mockEntityValidatorService,
        },
        {
          provide: UserProvider,
          useValue: mockUserProvider,
        },
        {
          provide: Notifier,
          useValue: mockNotifier,
        },
      ],
    }).compile();

    entityLikeNotificationService = module.get<EntityLikeNotificationService>(
      EntityLikeNotificationService,
    );
    entityCommentNotificationService =
      module.get<EntityCommentNotificationService>(
        EntityCommentNotificationService,
      );
  });

  describe('Entity Like Notifications', () => {
    it('should dispatch notification when entity is liked', async () => {
      // Arrange
      const payload = {
        likeId: 'like-123',
        entityType: 'video',
        entityId: 'entity-123',
        userId: 'user-123',
      };

      mockEntityValidatorService.getEntityInfo.mockResolvedValue({
        id: 'entity-123',
        ownerId: 'owner-123',
        title: 'Test Video',
        thumbnail: 'thumbnail.jpg',
      });

      mockUserProvider.get.mockResolvedValue({
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        includeWatermarks: false,
        hidePrompt: false,
        isActive: true,
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any);

      // Act
      await entityLikeNotificationService.handleEntityLiked(payload);

      // Assert
      expect(mockNotifier.dispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'video.liked',
          userId: 'owner-123',
          context: expect.objectContaining({
            likeId: 'like-123',
            entityType: 'video',
            entityId: 'entity-123',
            userId: 'user-123',
            entityTitle: 'Test Video',
            entityThumbnail: 'thumbnail.jpg',
            entityOwnerId: 'owner-123',
          }),
        }),
      );
    });
  });

  describe('Entity Comment Notifications', () => {
    it('should dispatch notification when entity is commented', async () => {
      // Arrange
      const payload = {
        commentId: 'comment-123',
        entityType: 'video',
        entityId: 'entity-123',
        userId: 'user-123',
        comment: 'Great video!',
      };

      mockEntityValidatorService.getEntityInfo.mockResolvedValue({
        id: 'entity-123',
        ownerId: 'owner-123',
        title: 'Test Video',
        thumbnail: 'thumbnail.jpg',
      });

      mockUserProvider.get.mockResolvedValue({
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        includeWatermarks: false,
        hidePrompt: false,
        isActive: true,
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any);

      // Act
      await entityCommentNotificationService.handleCommentCreated(payload);

      // Assert
      expect(mockNotifier.dispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'video.commented',
          userId: 'owner-123',
          context: expect.objectContaining({
            commentId: 'comment-123',
            entityType: 'video',
            entityId: 'entity-123',
            userId: 'user-123',
            comment: 'Great video!',
            entityTitle: 'Test Video',
            entityThumbnail: 'thumbnail.jpg',
            entityOwnerId: 'owner-123',
          }),
        }),
      );
    });
  });
});
