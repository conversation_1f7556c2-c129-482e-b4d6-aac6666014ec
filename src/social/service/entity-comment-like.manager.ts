import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { EntityCommentLikeEntity } from '../entity/entity-comment-like.entity';
import { EntityCommentEntity } from '../entity/entity-comment.entity';
import { AbstractEntityManager } from './abstract-entity.manager';
import { EntityCommentManager } from './entity-comment.manager';
import { EntityValidatorService } from './entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { UserResponseMapper } from '../../user/service/response-mapper';
import { Notifier } from '../../notification/service/notifier';
import { CommentLikedNotification } from '../notification/comment-liked.notification';

@Injectable()
export class EntityCommentLikeManager extends AbstractEntityManager<EntityCommentLikeEntity> {
  constructor(
    @InjectRepository(EntityCommentLikeEntity)
    repository: Repository<EntityCommentLikeEntity>,
    eventEmitter: EventEmitter2,
    logger: Logger,
    private entityCommentManager: EntityCommentManager,
    private entityValidatorService: EntityValidatorService,
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
    private notifier: Notifier,
  ) {
    super(repository, eventEmitter, logger);
  }

  /**
   * Like a comment
   */
  async likeComment(
    comment: EntityCommentEntity,
    userId: string,
  ): Promise<EntityCommentLikeEntity> {
    // Create comment like entity
    const commentLike = new EntityCommentLikeEntity();
    commentLike.entityCommentId = comment.id;
    commentLike.entityComment = comment;
    commentLike.userId = userId;

    // Save comment like
    const savedLike = await this.save(commentLike);

    // Increment like count on comment
    await this.entityCommentManager.updateCommentLikeCount(comment, true);

    // Get entity information for notification
    const entityInfo = await this.entityValidatorService.getEntityInfo(
      comment.entityType,
      comment.entityId,
    );

    // Get user information for notification
    const user = await this.userProvider.get(userId);

    // Send notification if user is not liking their own comment
    if (comment.userId !== userId && user) {
      await this.notifier.dispatch(
        new CommentLikedNotification(comment.userId, {
          id: comment.entityId,
          thumbnail: entityInfo.thumbnail,
          userId: comment.userId,
          likedById: userId,
          likedByUsername: user.username,
          likedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          likedAt: savedLike.createdAt,
          entityType: comment.entityType,
          entityTitle: entityInfo.title,
          commentId: comment.id,
          comment: comment.comment,
        }),
      );
    }

    // Emit event
    await this.emitEvent('entity.comment.liked', {
      commentLikeId: savedLike.id,
      commentId: comment.id,
      entityType: comment.entityType,
      entityId: comment.entityId,
      userId,
      commentOwnerId: comment.userId,
    });

    this.logger.log('Comment liked', {
      commentLikeId: savedLike.id,
      commentId: comment.id,
      userId,
    });

    return savedLike;
  }

  /**
   * Unlike a comment
   */
  async unlikeComment(
    commentLike: EntityCommentLikeEntity,
    comment: EntityCommentEntity,
  ): Promise<void> {
    const { userId } = commentLike;

    // Soft delete the comment like
    await this.delete(commentLike);

    // Decrement like count on comment
    await this.entityCommentManager.updateCommentLikeCount(comment, false);

    // Emit event
    await this.emitEvent('entity.comment.unliked', {
      commentLikeId: commentLike.id,
      commentId: comment.id,
      entityType: comment.entityType,
      entityId: comment.entityId,
      userId,
      commentOwnerId: comment.userId,
    });

    this.logger.log('Comment unliked', {
      commentLikeId: commentLike.id,
      commentId: comment.id,
      userId,
    });
  }

  /**
   * Toggle like status for a comment
   */
  async toggleCommentLike(
    comment: EntityCommentEntity,
    userId: string,
    existingLike: EntityCommentLikeEntity | null,
  ): Promise<{ liked: boolean; like?: EntityCommentLikeEntity }> {
    if (existingLike) {
      // Unlike
      await this.unlikeComment(existingLike, comment);
      return { liked: false };
    } else {
      // Like
      const like = await this.likeComment(comment, userId);
      return { liked: true, like };
    }
  }

  /**
   * Batch like multiple comments (for data migration or admin operations)
   */
  async batchLikeComments(
    likes: Array<{
      comment: EntityCommentEntity;
      userId: string;
    }>,
  ): Promise<EntityCommentLikeEntity[]> {
    const savedLikes: EntityCommentLikeEntity[] = [];

    for (const likeData of likes) {
      try {
        const like = await this.likeComment(likeData.comment, likeData.userId);
        savedLikes.push(like);
      } catch (error) {
        this.logger.error('Failed to create comment like in batch operation', {
          error: error.message,
          commentId: likeData.comment.id,
          userId: likeData.userId,
        });
        // Continue with other likes
      }
    }

    return savedLikes;
  }

  /**
   * Remove all likes for a comment (when comment is deleted)
   */
  async removeAllLikesForComment(commentId: string): Promise<number> {
    const result = await this.repository
      .createQueryBuilder()
      .softDelete()
      .where('entityCommentId = :commentId', { commentId })
      .execute();

    const deletedCount = result.affected || 0;

    this.logger.log('Removed all likes for comment', {
      commentId,
      deletedCount,
    });

    return deletedCount;
  }

  /**
   * Remove all comment likes by a user (when user is deleted)
   */
  async removeAllCommentLikesByUser(userId: string): Promise<number> {
    const result = await this.repository
      .createQueryBuilder()
      .softDelete()
      .where('userId = :userId', { userId })
      .execute();

    const deletedCount = result.affected || 0;

    this.logger.log('Removed all comment likes by user', {
      userId,
      deletedCount,
    });

    return deletedCount;
  }

  /**
   * Remove all likes for comments of a specific entity (when entity is deleted)
   */
  async removeAllLikesForEntityComments(
    entityType: string,
    entityId: string,
  ): Promise<number> {
    const result = await this.repository
      .createQueryBuilder('commentLike')
      .leftJoin('commentLike.entityComment', 'comment')
      .softDelete()
      .where('comment.entityType = :entityType', { entityType })
      .andWhere('comment.entityId = :entityId', { entityId })
      .execute();

    const deletedCount = result.affected || 0;

    this.logger.log('Removed all comment likes for entity', {
      entityType,
      entityId,
      deletedCount,
    });

    return deletedCount;
  }

  /**
   * Validate comment like entity before operations
   */
  protected async validateEntity(
    entity: EntityCommentLikeEntity,
  ): Promise<void> {
    if (!entity.entityCommentId) {
      throw new Error('Comment ID is required');
    }
    if (!entity.userId) {
      throw new Error('User ID is required');
    }
  }

  /**
   * Pre-save hook for comment likes
   */
  protected async preSave(entity: EntityCommentLikeEntity): Promise<void> {
    // No specific pre-save logic needed for comment likes
  }

  /**
   * Post-save hook for comment likes
   */
  protected async postSave(entity: EntityCommentLikeEntity): Promise<void> {
    // Could add analytics tracking here if needed
  }

  /**
   * Pre-delete hook for comment likes
   */
  protected async preDelete(entity: EntityCommentLikeEntity): Promise<void> {
    // No specific pre-delete logic needed
  }

  /**
   * Post-delete hook for comment likes
   */
  protected async postDelete(entity: EntityCommentLikeEntity): Promise<void> {
    // Could add analytics tracking here if needed
  }
}
