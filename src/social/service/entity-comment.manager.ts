import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { EntityCommentEntity } from '../entity/entity-comment.entity';
import { AbstractEntityManager } from './abstract-entity.manager';
import { EntityValidator } from './entity-validator.interface';
import { EntityValidatorService } from './entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { UserResponseMapper } from '../../user/service/response-mapper';
import { Notifier } from '../../notification/service/notifier';
import { EntityCommentedNotification } from '../notification/entity-commented.notification';

@Injectable()
export class EntityCommentManager extends AbstractEntityManager<EntityCommentEntity> {
  constructor(
    @InjectRepository(EntityCommentEntity)
    repository: Repository<EntityCommentEntity>,
    eventEmitter: EventEmitter2,
    logger: Logger,
    private entityValidatorService: EntityValidatorService,
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
    private notifier: Notifier,
  ) {
    super(repository, eventEmitter, logger);
  }

  /**
   * Create a new comment on an entity
   */
  async createComment(
    entityType: string,
    entityId: string,
    userId: string,
    comment: string,
    entityValidator: EntityValidator,
  ): Promise<EntityCommentEntity> {
    // Validate entity exists
    await entityValidator.validateExists(entityId);

    // Create comment entity
    const entityComment = new EntityCommentEntity();
    entityComment.entityType = entityType;
    entityComment.entityId = entityId;
    entityComment.userId = userId;
    entityComment.comment = comment;
    entityComment.likes = 0;

    // Save comment
    const savedComment = await this.save(entityComment);

    // Get entity information for notification
    const entityInfo = await this.entityValidatorService.getEntityInfo(
      entityType,
      entityId,
    );

    // Get user information for notification
    const user = await this.userProvider.get(userId);

    // Send notification if user is not commenting on their own entity
    if (entityInfo.ownerId !== userId && user) {
      await this.notifier.dispatch(
        new EntityCommentedNotification(entityInfo.ownerId, {
          id: entityId,
          thumbnail: entityInfo.thumbnail,
          userId: entityInfo.ownerId,
          commentedById: userId,
          commentedByUsername: user.username,
          commentedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          commentedAt: savedComment.createdAt,
          entityType: entityType,
          entityTitle: entityInfo.title,
          comment: comment,
        }),
      );
    }

    // Emit event (this will trigger counter update via event listener)
    await this.eventEmitter.emit('entity.comment.created', {
      commentId: savedComment.id,
      entityType,
      entityId,
      userId,
      comment,
    });

    this.logger.log('Comment created', {
      commentId: savedComment.id,
      entityType,
      entityId,
      userId,
    });

    return savedComment;
  }

  /**
   * Delete a comment and update parent entity count
   */
  async deleteComment(
    comment: EntityCommentEntity,
    entityValidator: EntityValidator,
  ): Promise<void> {
    const { entityType, entityId, userId } = comment;

    // Soft delete the comment
    await this.delete(comment);

    // Emit event (this will trigger counter update via event listener)
    await this.emitEvent('entity.comment.deleted', {
      commentId: comment.id,
      entityType,
      entityId,
      userId,
    });

    this.logger.log('Comment deleted', {
      commentId: comment.id,
      entityType,
      entityId,
      userId,
    });
  }

  /**
   * Update like count on a comment
   */
  async updateCommentLikeCount(
    comment: EntityCommentEntity,
    increment: boolean,
  ): Promise<void> {
    if (increment) {
      comment.likes++;
    } else {
      comment.likes = Math.max(0, comment.likes - 1);
    }

    await this.save(comment);

    this.logger.debug('Comment like count updated', {
      commentId: comment.id,
      likes: comment.likes,
      increment,
    });
  }

  /**
   * Validate comment entity before operations
   */
  protected async validateEntity(entity: EntityCommentEntity): Promise<void> {
    if (!entity.entityType) {
      throw new Error('Entity type is required');
    }
    if (!entity.entityId) {
      throw new Error('Entity ID is required');
    }
    if (!entity.userId) {
      throw new Error('User ID is required');
    }
    if (!entity.comment || entity.comment.trim().length === 0) {
      throw new Error('Comment text is required');
    }
    if (entity.comment.length > 2000) {
      throw new Error('Comment text is too long (max 2000 characters)');
    }
  }

  /**
   * Pre-save hook for comments
   */
  protected async preSave(entity: EntityCommentEntity): Promise<void> {
    // Trim comment text
    if (entity.comment) {
      entity.comment = entity.comment.trim();
    }
  }
}
