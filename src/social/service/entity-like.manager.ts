import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { EntityLikeEntity } from '../entity/entity-like.entity';
import { AbstractEntityManager } from './abstract-entity.manager';
import { EntityValidator } from './entity-validator.interface';
import { EntityValidatorService } from './entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { UserResponseMapper } from '../../user/service/response-mapper';
import { Notifier } from '../../notification/service/notifier';
import { EntityLikedNotification } from '../notification/entity-liked.notification';

@Injectable()
export class EntityLikeManager extends AbstractEntityManager<EntityLikeEntity> {
  constructor(
    @InjectRepository(EntityLikeEntity)
    repository: Repository<EntityLikeEntity>,
    eventEmitter: EventEmitter2,
    logger: Logger,
    private entityValidatorService: EntityValidatorService,
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
    private notifier: Notifier,
  ) {
    super(repository, eventEmitter, logger);
  }

  /**
   * Like an entity
   */
  async likeEntity(
    entityType: string,
    entityId: string,
    userId: string,
    entityValidator: EntityValidator,
  ): Promise<EntityLikeEntity> {
    // Validate entity exists
    await entityValidator.validateExists(entityId);

    // Create like entity
    const entityLike = new EntityLikeEntity();
    entityLike.entityType = entityType;
    entityLike.entityId = entityId;
    entityLike.userId = userId;

    // Save like
    const savedLike = await this.save(entityLike);

    // Get entity information for notification
    const entityInfo = await this.entityValidatorService.getEntityInfo(
      entityType,
      entityId,
    );

    // Get user information for notification
    const user = await this.userProvider.get(userId);

    // Send notification if user is not liking their own entity
    if (entityInfo.ownerId !== userId && user) {
      await this.notifier.dispatch(
        new EntityLikedNotification(entityInfo.ownerId, {
          id: entityId,
          thumbnail: entityInfo.thumbnail,
          userId: entityInfo.ownerId,
          likedById: userId,
          likedByUsername: user.username,
          likedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          likedAt: savedLike.createdAt,
          entityType: entityType,
          entityTitle: entityInfo.title,
        }),
      );
    }

    // Emit event (this will trigger counter update via event listener)
    await this.emitEvent('entity.liked', {
      likeId: savedLike.id,
      entityType,
      entityId,
      userId,
    });

    this.logger.log('Entity liked', {
      likeId: savedLike.id,
      entityType,
      entityId,
      userId,
    });

    return savedLike;
  }

  /**
   * Unlike an entity
   */
  async unlikeEntity(
    like: EntityLikeEntity,
    entityValidator: EntityValidator,
  ): Promise<void> {
    const { entityType, entityId, userId } = like;

    // Soft delete the like
    await this.delete(like);

    // Emit event (this will trigger counter update via event listener)
    await this.emitEvent('entity.unliked', {
      likeId: like.id,
      entityType,
      entityId,
      userId,
    });

    this.logger.log('Entity unliked', {
      likeId: like.id,
      entityType,
      entityId,
      userId,
    });
  }

  /**
   * Toggle like status for an entity
   */
  async toggleEntityLike(
    entityType: string,
    entityId: string,
    userId: string,
    existingLike: EntityLikeEntity | null,
    entityValidator: EntityValidator,
  ): Promise<{ liked: boolean; like?: EntityLikeEntity }> {
    if (existingLike) {
      // Unlike
      await this.unlikeEntity(existingLike, entityValidator);
      return { liked: false };
    } else {
      // Like
      const like = await this.likeEntity(
        entityType,
        entityId,
        userId,
        entityValidator,
      );
      return { liked: true, like };
    }
  }

  /**
   * Batch like multiple entities (for data migration or admin operations)
   */
  async batchLikeEntities(
    likes: Array<{
      entityType: string;
      entityId: string;
      userId: string;
    }>,
    entityValidator: EntityValidator,
  ): Promise<EntityLikeEntity[]> {
    const savedLikes: EntityLikeEntity[] = [];

    for (const likeData of likes) {
      try {
        const like = await this.likeEntity(
          likeData.entityType,
          likeData.entityId,
          likeData.userId,
          entityValidator,
        );
        savedLikes.push(like);
      } catch (error) {
        this.logger.error('Failed to create like in batch operation', {
          error: error.message,
          likeData,
        });
        // Continue with other likes
      }
    }

    return savedLikes;
  }

  /**
   * Remove all likes for an entity (when entity is deleted)
   */
  async removeAllLikesForEntity(
    entityType: string,
    entityId: string,
  ): Promise<number> {
    const result = await this.repository
      .createQueryBuilder()
      .softDelete()
      .where('entityType = :entityType', { entityType })
      .andWhere('entityId = :entityId', { entityId })
      .execute();

    const deletedCount = result.affected || 0;

    this.logger.log('Removed all likes for entity', {
      entityType,
      entityId,
      deletedCount,
    });

    return deletedCount;
  }

  /**
   * Remove all likes by a user (when user is deleted)
   */
  async removeAllLikesByUser(userId: string): Promise<number> {
    const result = await this.repository
      .createQueryBuilder()
      .softDelete()
      .where('userId = :userId', { userId })
      .execute();

    const deletedCount = result.affected || 0;

    this.logger.log('Removed all likes by user', {
      userId,
      deletedCount,
    });

    return deletedCount;
  }

  /**
   * Validate like entity before operations
   */
  protected async validateEntity(entity: EntityLikeEntity): Promise<void> {
    if (!entity.entityType) {
      throw new Error('Entity type is required');
    }
    if (!entity.entityId) {
      throw new Error('Entity ID is required');
    }
    if (!entity.userId) {
      throw new Error('User ID is required');
    }
  }

  /**
   * Pre-save hook for likes
   */
  protected async preSave(entity: EntityLikeEntity): Promise<void> {
    // Normalize entity type to lowercase
    if (entity.entityType) {
      entity.entityType = entity.entityType.toLowerCase();
    }
  }
}
