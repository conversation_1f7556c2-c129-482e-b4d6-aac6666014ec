import {
  IsOptional,
  IsString,
  IsInt,
  <PERSON>,
  <PERSON>,
  <PERSON>In,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { SortOrderTransformer } from 'src/core/transformer/sort-order.transformer';

export class EntityLikeCursorSearchRequest {
  @ApiProperty({
    description:
      'Cursor for pagination (timestamp or ID of last item from previous page)',
    example: '2024-01-15T10:30:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsString()
  cursor?: string;

  @ApiProperty({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
    default: 20,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must be at most 100' })
  limit = 20;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: ['createdAt'],
    default: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['createdAt'], { message: 'Sort by must be createdAt' })
  sortBy = 'createdAt';

  @ApiProperty({
    description: 'Sort order (case insensitive)',
    example: 'DESC',
    enum: ['ASC', 'DESC', 'asc', 'desc'],
    default: 'DESC',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC', 'asc', 'desc'], {
    message: 'Sort order must be either ASC, DESC, asc, or desc',
  })
  @SortOrderTransformer()
  sortOrder: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({
    description: 'Filter by username',
    example: 'john_doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({
    description: 'Include entity information in response',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  includeEntity = false;

  @ApiProperty({
    description: 'Array of model IDs to filter by',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true, message: 'Each model ID must be a string' })
  modelIds?: string[];
}
